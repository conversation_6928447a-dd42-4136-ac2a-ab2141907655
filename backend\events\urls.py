from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import EventViewSet, EventScheduleViewSet, EventGalleryViewSet
from .email_views import EmailConfigurationViewSet, EmailTemplateViewSet, EmailLogViewSet, EmailNotificationViewSet

router = DefaultRouter()
router.register(r'events', EventViewSet)
router.register(r'schedules', EventScheduleViewSet)
router.register(r'gallery', EventGalleryViewSet)
router.register(r'email-configs', EmailConfigurationViewSet)
router.register(r'email-templates', EmailTemplateViewSet)
router.register(r'email-logs', EmailLogViewSet)
router.register(r'email-notifications', EmailNotificationViewSet, basename='email-notifications')

urlpatterns = [
    path('', include(router.urls)),
    path('public/events/', EventViewSet.as_view({'get': 'list'}), name='public-events'),
]