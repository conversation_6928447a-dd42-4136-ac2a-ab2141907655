#!/bin/bash

# University of Gondar Event Management System
# Let's Encrypt SSL Certificate Initialization Script
# This script safely initializes SSL certificates for event.uog.edu.et

set -e

# Configuration
DOMAIN="event.uog.edu.et"
EMAIL="<EMAIL>"  # Replace with your actual email
STAGING=0  # Set to 1 for testing with Let's Encrypt staging environment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 University of Gondar Event Management System${NC}"
echo -e "${BLUE}🚀 Let's Encrypt SSL Certificate Initialization${NC}"
echo -e "${BLUE}================================================${NC}"

# Check if domain is accessible
echo -e "\n${YELLOW}🔍 Checking domain accessibility...${NC}"
if ! ping -c 1 $DOMAIN &> /dev/null; then
    echo -e "${RED}❌ Error: Domain $DOMAIN is not accessible from this server${NC}"
    echo -e "${RED}   Please ensure:${NC}"
    echo -e "${RED}   1. DNS A record points to this server's IP${NC}"
    echo -e "${RED}   2. Firewall allows HTTP (port 80) and HTTPS (port 443)${NC}"
    echo -e "${RED}   3. Domain is properly configured${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Domain $DOMAIN is accessible${NC}"

# Check if certificates already exist
if [ -d "./certbot/conf/live/$DOMAIN" ]; then
    echo -e "\n${YELLOW}⚠️  SSL certificates for $DOMAIN already exist${NC}"
    read -p "Do you want to renew them? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}ℹ️  Skipping certificate generation${NC}"
        exit 0
    fi
fi

# Create necessary directories
echo -e "\n${YELLOW}📁 Creating certificate directories...${NC}"
mkdir -p ./certbot/conf
mkdir -p ./certbot/www
mkdir -p ./logs/certbot

# Stop any running containers
echo -e "\n${YELLOW}🛑 Stopping existing containers...${NC}"
docker-compose -f docker-compose.prod.yml down

# Create temporary nginx config for certificate generation
echo -e "\n${YELLOW}⚙️  Creating temporary nginx configuration...${NC}"
cat > ./nginx/conf.d/temp-ssl.conf << EOF
# Temporary configuration for SSL certificate generation
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;

    # Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
    }

    # Temporary location for other requests during setup
    location / {
        return 200 'SSL certificate generation in progress...';
        add_header Content-Type text/plain;
    }
}
EOF

# Backup original config
if [ -f "./nginx/conf.d/default.conf" ]; then
    cp ./nginx/conf.d/default.conf ./nginx/conf.d/default.conf.backup
    echo -e "${GREEN}✅ Backed up original nginx configuration${NC}"
fi

# Temporarily disable SSL config
mv ./nginx/conf.d/default.conf ./nginx/conf.d/default.conf.ssl

# Start nginx with temporary config
echo -e "\n${YELLOW}🚀 Starting nginx with temporary configuration...${NC}"
docker-compose -f docker-compose.prod.yml up -d nginx

# Wait for nginx to be ready
echo -e "${YELLOW}⏳ Waiting for nginx to be ready...${NC}"
sleep 10

# Test nginx is responding
if ! curl -f http://$DOMAIN/.well-known/acme-challenge/test &> /dev/null; then
    echo -e "${YELLOW}ℹ️  Nginx is starting up, this is normal...${NC}"
fi

# Generate SSL certificate
echo -e "\n${YELLOW}🔐 Generating SSL certificate for $DOMAIN...${NC}"

if [ $STAGING != "0" ]; then
    echo -e "${YELLOW}⚠️  Using Let's Encrypt STAGING environment (for testing)${NC}"
    STAGING_ARG="--staging"
else
    echo -e "${GREEN}🔥 Using Let's Encrypt PRODUCTION environment${NC}"
    STAGING_ARG=""
fi

# Run certbot
docker-compose -f docker-compose.prod.yml run --rm certbot \
    certonly \
    --webroot \
    --webroot-path=/var/www/certbot \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    --force-renewal \
    $STAGING_ARG \
    -d $DOMAIN \
    -d www.$DOMAIN

# Check if certificate was generated successfully
if [ ! -f "./certbot/conf/live/$DOMAIN/fullchain.pem" ]; then
    echo -e "${RED}❌ Error: SSL certificate generation failed${NC}"
    echo -e "${RED}   Check the logs above for details${NC}"
    
    # Restore original config
    if [ -f "./nginx/conf.d/default.conf.backup" ]; then
        mv ./nginx/conf.d/default.conf.backup ./nginx/conf.d/default.conf
    fi
    rm -f ./nginx/conf.d/temp-ssl.conf
    
    exit 1
fi

echo -e "${GREEN}✅ SSL certificate generated successfully!${NC}"

# Restore SSL-enabled nginx configuration
echo -e "\n${YELLOW}⚙️  Restoring SSL-enabled nginx configuration...${NC}"
mv ./nginx/conf.d/default.conf.ssl ./nginx/conf.d/default.conf
rm -f ./nginx/conf.d/temp-ssl.conf

# Update backend environment for HTTPS
echo -e "\n${YELLOW}⚙️  Updating backend configuration for HTTPS...${NC}"

# Stop containers
docker-compose -f docker-compose.prod.yml down

# Start all services with SSL
echo -e "\n${YELLOW}🚀 Starting all services with SSL enabled...${NC}"
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be ready
echo -e "${YELLOW}⏳ Waiting for services to be ready...${NC}"
sleep 30

# Test HTTPS connection
echo -e "\n${YELLOW}🧪 Testing HTTPS connection...${NC}"
if curl -f -s https://$DOMAIN/health/ > /dev/null; then
    echo -e "${GREEN}✅ HTTPS is working correctly!${NC}"
else
    echo -e "${YELLOW}⚠️  HTTPS test failed, but this might be normal during startup${NC}"
    echo -e "${YELLOW}   Please wait a few minutes and test manually${NC}"
fi

# Display certificate information
echo -e "\n${BLUE}📋 Certificate Information:${NC}"
docker-compose -f docker-compose.prod.yml run --rm certbot certificates

echo -e "\n${GREEN}🎉 SSL setup completed successfully!${NC}"
echo -e "${GREEN}================================================${NC}"
echo -e "${GREEN}✅ Your University of Gondar Event Management System is now secured with SSL${NC}"
echo -e "${GREEN}🌐 Access your application at: https://$DOMAIN${NC}"
echo -e "${GREEN}🔒 SSL certificate will auto-renew every 12 hours${NC}"
echo -e "${GREEN}📧 Certificate notifications will be sent to: $EMAIL${NC}"
echo -e "\n${BLUE}ℹ️  Important Notes:${NC}"
echo -e "${BLUE}   • Certificates are stored in ./certbot/conf/live/$DOMAIN/${NC}"
echo -e "${BLUE}   • Auto-renewal is configured via the certbot container${NC}"
echo -e "${BLUE}   • Monitor logs in ./logs/certbot/ for renewal status${NC}"
echo -e "${BLUE}   • Update your DNS if you change server IP${NC}"

# Clean up backup
if [ -f "./nginx/conf.d/default.conf.backup" ]; then
    rm ./nginx/conf.d/default.conf.backup
fi

echo -e "\n${GREEN}🔐 SSL initialization complete! 🔐${NC}"
