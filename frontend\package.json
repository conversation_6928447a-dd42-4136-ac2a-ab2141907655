{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/jszip": "^3.4.0", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^19.1.6", "@types/react-router-bootstrap": "^0.26.8", "@types/react-router-dom": "^5.3.3", "axios": "^1.11.0", "bootstrap": "^5.3.7", "html5-qrcode": "^2.3.8", "jszip": "^3.10.1", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-qr-code": "^2.0.18", "react-router-bootstrap": "^0.26.3", "react-router-dom": "^7.7.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}