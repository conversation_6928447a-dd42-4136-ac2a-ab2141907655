import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Alert, Form, Modal } from 'react-bootstrap';
import { Html5QrcodeScanner } from 'html5-qrcode';
import { participantService, attendanceService, eventService, Event, EventSchedule } from '../services/api';

const QRScanner: React.FC = () => {
  const [scanning, setScanning] = useState(false);
  const [scannedData, setScannedData] = useState<any>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<string>('');
  const [eventSchedules, setEventSchedules] = useState<EventSchedule[]>([]);
  const [selectedSchedule, setSelectedSchedule] = useState<string>('');
  const [checkedInBy, setCheckedInBy] = useState('');
  const [notes, setNotes] = useState('');
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  const [showCheckInModal, setShowCheckInModal] = useState(false);

  useEffect(() => {
    fetchEvents();
  }, []);

  useEffect(() => {
    if (selectedEvent) {
      fetchEventSchedules();
    }
  }, [selectedEvent]);

  const fetchEvents = async () => {
    try {
      const response = await eventService.getEvents();
      const eventsData = response.data.results || [];
      setEvents(eventsData.filter((event: any) => event.is_active));
    } catch (error) {
      console.error('Error fetching events:', error);
    }
  };

  const fetchEventSchedules = async () => {
    try {
      const response = await eventService.getEventSchedule(parseInt(selectedEvent));
      setEventSchedules(response.data);
    } catch (error) {
      console.error('Error fetching schedules:', error);
    }
  };

  const startScanning = () => {
    setScanning(true);
    setError('');
    setSuccess('');

    const scanner = new Html5QrcodeScanner(
      'qr-reader',
      { fps: 10, qrbox: { width: 250, height: 250 } },
      false
    );

    scanner.render(
      (decodedText, decodedResult) => {
        // Handle successful scan
        handleScanSuccess(decodedText);
        scanner.clear();
        setScanning(false);
      },
      (error) => {
        // Handle scan error (can be ignored for continuous scanning)
        console.warn('QR scan error:', error);
      }
    );
  };

  const stopScanning = () => {
    setScanning(false);
    // Clear the scanner
    const element = document.getElementById('qr-reader');
    if (element) {
      element.innerHTML = '';
    }
  };

  const handleScanSuccess = async (decodedText: string) => {
    try {
      // Extract UUID from the scanned URL
      const urlParts = decodedText.split('/');
      const uuid = urlParts[urlParts.length - 1];

      // Verify participant
      const response = await participantService.verifyParticipant(uuid);
      setScannedData(response.data);
      setShowCheckInModal(true);
    } catch (error: any) {
      setError('Invalid QR code or participant not found');
    }
  };

  const handleCheckIn = async () => {
    if (!selectedSchedule) {
      setError('Please select an event schedule');
      return;
    }

    try {
      await attendanceService.checkIn({
        uuid: scannedData.uuid,
        event_schedule_id: parseInt(selectedSchedule),
        checked_in_by: checkedInBy,
        notes: notes,
      });

      setSuccess(`Successfully checked in ${scannedData.full_name}`);
      setShowCheckInModal(false);
      setScannedData(null);
      setNotes('');
    } catch (error: any) {
      setError(error.response?.data?.error || 'Check-in failed');
    }
  };

  return (
    <Container className="py-4">
      <Row className="justify-content-center">
        <Col lg={8}>
          <Card className="shadow">
            <Card.Header className="bg-primary text-white">
              <h3 className="mb-0">
                <i className="fas fa-qrcode me-2"></i>
                QR Code Scanner
              </h3>
            </Card.Header>
            <Card.Body className="p-4">
              {success && (
                <Alert variant="success" className="mb-4">
                  <i className="fas fa-check-circle me-2"></i>
                  {success}
                </Alert>
              )}

              {error && (
                <Alert variant="danger" className="mb-4">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              {/* Event and Schedule Selection */}
              <Row className="mb-4">
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Select Event</Form.Label>
                    <Form.Select
                      value={selectedEvent}
                      onChange={(e) => setSelectedEvent(e.target.value)}
                      required
                    >
                      <option value="">Choose an event...</option>
                      {events.map(event => (
                        <option key={event.id} value={event.id}>
                          {event.name}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Select Session</Form.Label>
                    <Form.Select
                      value={selectedSchedule}
                      onChange={(e) => setSelectedSchedule(e.target.value)}
                      disabled={!selectedEvent}
                      required
                    >
                      <option value="">Choose a session...</option>
                      {eventSchedules.map(schedule => (
                        <option key={schedule.id} value={schedule.id}>
                          {schedule.title} - {new Date(schedule.start_time).toLocaleString()}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Row className="mb-4">
                <Col>
                  <Form.Group>
                    <Form.Label>Checked in by (Staff Name)</Form.Label>
                    <Form.Control
                      type="text"
                      value={checkedInBy}
                      onChange={(e) => setCheckedInBy(e.target.value)}
                      placeholder="Enter your name"
                    />
                  </Form.Group>
                </Col>
              </Row>

              {/* Scanner Controls */}
              <div className="text-center mb-4">
                {!scanning ? (
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={startScanning}
                    disabled={!selectedEvent || !selectedSchedule}
                  >
                    <i className="fas fa-camera me-2"></i>
                    Start Scanning
                  </Button>
                ) : (
                  <Button
                    variant="danger"
                    size="lg"
                    onClick={stopScanning}
                  >
                    <i className="fas fa-stop me-2"></i>
                    Stop Scanning
                  </Button>
                )}
              </div>

              {/* QR Scanner */}
              <div id="qr-reader" className="mb-4"></div>

              {/* Instructions */}
              <Alert variant="info">
                <h6><i className="fas fa-info-circle me-2"></i>Instructions:</h6>
                <ul className="mb-0">
                  <li>Select an event and session before scanning</li>
                  <li>Point your camera at the participant's badge QR code</li>
                  <li>The system will automatically detect and process the code</li>
                  <li>Confirm the check-in details in the popup</li>
                </ul>
              </Alert>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Check-in Confirmation Modal */}
      <Modal show={showCheckInModal} onHide={() => setShowCheckInModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Check-in</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {scannedData && (
            <div>
              <h5>Participant Details:</h5>
              <p><strong>Name:</strong> {scannedData.full_name}</p>
              <p><strong>Email:</strong> {scannedData.email}</p>
              <p><strong>Institution:</strong> {scannedData.institution_name}</p>
              <p><strong>Type:</strong>
                <span
                  className="badge ms-2"
                  style={{ backgroundColor: scannedData.participant_type_color }}
                >
                  {scannedData.participant_type_name}
                </span>
              </p>

              <Form.Group className="mt-3">
                <Form.Label>Notes (Optional)</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={2}
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Any additional notes..."
                />
              </Form.Group>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCheckInModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleCheckIn}>
            <i className="fas fa-check me-2"></i>
            Confirm Check-in
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default QRScanner;
