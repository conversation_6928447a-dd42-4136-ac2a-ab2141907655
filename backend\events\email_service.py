import os
import zipfile
import tempfile
from datetime import datetime, timedelta
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from .models import EmailConfiguration, EmailTemplate, EmailLog, EventGallery
from participants.models import Participant


class EmailService:
    """Service class for handling email operations"""
    
    def __init__(self):
        self.config = self._get_active_config()
    
    def _get_active_config(self):
        """Get the active email configuration"""
        try:
            return EmailConfiguration.objects.get(is_active=True)
        except EmailConfiguration.DoesNotExist:
            return None
    
    def _get_template(self, template_type):
        """Get the active email template for a specific type"""
        try:
            return EmailTemplate.objects.get(template_type=template_type, is_active=True)
        except EmailTemplate.DoesNotExist:
            return None
    
    def _log_email(self, recipient_email, recipient_name, subject, template_type, status='pending', error_message=''):
        """Log email sending attempt"""
        return EmailLog.objects.create(
            recipient_email=recipient_email,
            recipient_name=recipient_name,
            subject=subject,
            template_type=template_type,
            status=status,
            error_message=error_message,
            sent_at=timezone.now() if status == 'sent' else None
        )
    
    def send_email(self, template_type, recipient_email, recipient_name='', context=None, attachments=None):
        """
        Send email using the specified template type
        
        Args:
            template_type: Type of email template to use
            recipient_email: Recipient's email address
            recipient_name: Recipient's name (optional)
            context: Dictionary of context variables for template rendering
            attachments: List of file paths to attach
        
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        if not self.config:
            print("No active email configuration found")
            return False
        
        template = self._get_template(template_type)
        if not template:
            print(f"No active template found for type: {template_type}")
            return False
        
        context = context or {}
        context.update({
            'recipient_name': recipient_name,
            'site_name': 'University of Gondar Events',
            'current_year': datetime.now().year,
            # Default contact person information
            'contact_person_name': 'Event Coordinator',
            'contact_person_title': 'University of Gondar Events Team',
            'contact_person_email': '<EMAIL>',
            'contact_person_phone': '+251-58-114-0481',
        })
        
        # Render email content
        try:
            subject = template.subject.format(**context)
            html_content = template.html_content.format(**context)
            text_content = template.text_content.format(**context) if template.text_content else None
        except KeyError as e:
            error_msg = f"Template rendering error: Missing context variable {e}"
            self._log_email(recipient_email, recipient_name, template.subject, template_type, 'failed', error_msg)
            print(error_msg)
            return False
        
        # Create email log entry
        email_log = self._log_email(recipient_email, recipient_name, subject, template_type)
        
        try:
            # Apply email configuration to Django settings
            from django.conf import settings
            original_settings = {}

            # Store original settings
            email_settings = [
                'EMAIL_BACKEND', 'EMAIL_HOST', 'EMAIL_PORT', 'EMAIL_USE_TLS',
                'EMAIL_USE_SSL', 'EMAIL_HOST_USER', 'EMAIL_HOST_PASSWORD', 'DEFAULT_FROM_EMAIL'
            ]

            for setting in email_settings:
                if hasattr(settings, setting):
                    original_settings[setting] = getattr(settings, setting)

            # Apply database configuration
            settings.EMAIL_BACKEND = self.config.email_backend
            settings.EMAIL_HOST = self.config.email_host
            settings.EMAIL_PORT = self.config.email_port
            settings.EMAIL_USE_TLS = self.config.email_use_tls
            settings.EMAIL_USE_SSL = self.config.email_use_ssl
            settings.EMAIL_HOST_USER = self.config.email_host_user
            settings.EMAIL_HOST_PASSWORD = self.config.email_host_password
            settings.DEFAULT_FROM_EMAIL = self.config.default_from_email

            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content or html_content,
                from_email=self.config.default_from_email,
                to=[recipient_email]
            )

            if html_content:
                email.attach_alternative(html_content, "text/html")

            # Add attachments if provided
            if attachments:
                for attachment_path in attachments:
                    if os.path.exists(attachment_path):
                        email.attach_file(attachment_path)

            # Send email
            email.send()

            # Restore original settings
            for key, value in original_settings.items():
                setattr(settings, key, value)

            # Update log status
            email_log.status = 'sent'
            email_log.sent_at = timezone.now()
            email_log.save()

            return True
            
        except Exception as e:
            # Restore original settings in case of error
            try:
                for key, value in original_settings.items():
                    setattr(settings, key, value)
            except:
                pass

            error_msg = str(e)
            email_log.status = 'failed'
            email_log.error_message = error_msg
            email_log.save()
            print(f"Failed to send email: {error_msg}")
            return False

    def _get_contact_person_context(self, participant):
        """Get contact person context for participant"""
        if participant and participant.assigned_contact_person:
            contact = participant.assigned_contact_person
            return {
                'contact_person_name': contact.full_name,
                'contact_person_title': contact.position or 'Contact Person',
                'contact_person_email': contact.email,
                'contact_person_phone': contact.phone,
            }
        return {}  # Will use defaults from send_email method

    def send_registration_confirmation(self, participant):
        """Send registration confirmation email"""
        context = {
            'participant_name': participant.full_name,
            'participant_email': participant.email,
            'participant_phone': participant.phone or 'Not provided',
            'participant_organization': participant.institution_name or 'Not provided',
            'participant_type': participant.participant_type.name if participant.participant_type else 'Not specified',
            'event_name': participant.event.name,
            'event_description': participant.event.description or 'Event details will be provided upon approval.',
            'event_start_date': participant.event.start_date.strftime('%B %d, %Y at %I:%M %p'),
            'event_end_date': participant.event.end_date.strftime('%B %d, %Y at %I:%M %p'),
            'event_date': participant.event.start_date.strftime('%B %d, %Y'),  # For backward compatibility
            'event_location': participant.event.location,
            'registration_date': participant.registration_date.strftime('%B %d, %Y at %I:%M %p'),
            'organizer_name': participant.event.organizer_name or 'Event Organizer',
            'organizer_email': participant.event.organizer_email or '<EMAIL>',
            'organizer_phone': participant.event.organizer_phone or '+251-58-114-0481',
        }

        # Add contact person context
        context.update(self._get_contact_person_context(participant))

        return self.send_email(
            'registration_confirmation',
            participant.email,
            participant.full_name,
            context
        )
    
    def send_event_details(self, participant):
        """Send detailed event information with assignments and badge"""
        # Build assignment sections
        hotel_section = self._build_hotel_section(participant)
        driver_section = self._build_driver_section(participant)
        contact_person_section = self._build_contact_person_section(participant)

        context = {
            'participant_name': participant.full_name,
            'event_name': participant.event.name,
            'event_description': participant.event.description,
            'event_start_date': participant.event.start_date.strftime('%B %d, %Y at %I:%M %p'),
            'event_end_date': participant.event.end_date.strftime('%B %d, %Y at %I:%M %p'),
            'event_location': participant.event.location,
            'organizer_name': participant.event.organizer_name,
            'organizer_email': participant.event.organizer_email,
            'organizer_phone': participant.event.organizer_phone,
            'hotel_section': hotel_section,
            'driver_section': driver_section,
            'contact_person_section': contact_person_section,
        }

        # Add contact person context
        context.update(self._get_contact_person_context(participant))

        # Prepare badge attachment if available
        attachments = []
        try:
            if hasattr(participant, 'badge') and participant.badge.badge_image:
                if participant.badge.badge_image.path and os.path.exists(participant.badge.badge_image.path):
                    attachments.append(participant.badge.badge_image.path)
        except Exception as e:
            print(f"Could not attach badge for {participant.full_name}: {e}")
            pass  # Badge file might not exist

        return self.send_email(
            'event_details',
            participant.email,
            participant.full_name,
            context,
            attachments=attachments
        )
    
    def send_badge_notification(self, participant, badge_path=None):
        """Send badge notification with attachment"""
        context = {
            'participant_name': participant.full_name,
            'event_name': participant.event.name,
        }

        # Add contact person context
        context.update(self._get_contact_person_context(participant))

        attachments = [badge_path] if badge_path and os.path.exists(badge_path) else None

        return self.send_email(
            'badge_notification',
            participant.email,
            participant.full_name,
            context,
            attachments
        )
    
    def send_schedule_update(self, participants, event):
        """Send schedule updates to multiple participants"""
        # Build schedule rows HTML and text
        schedules = event.schedules.all().order_by('start_time')
        schedule_rows = ""
        schedule_text = ""

        if schedules.exists():
            for schedule in schedules:
                start_time = schedule.start_time.strftime('%H:%M') if schedule.start_time else 'TBA'
                end_time = schedule.end_time.strftime('%H:%M') if schedule.end_time else 'TBA'
                time_range = f"{start_time} - {end_time}"

                # HTML version
                schedule_rows += f'''
                <tr style="border-bottom: 1px solid #dee2e6;">
                    <td style="padding: 12px; color: #17a2b8; font-weight: bold;">
                        {time_range}
                    </td>
                    <td style="padding: 12px; color: #333;">
                        <strong>{schedule.title}</strong>
                        {f'<br><small style="color: #666;">{schedule.description}</small>' if schedule.description else ''}
                    </td>
                    <td style="padding: 12px; color: #333;">
                        {schedule.speaker or 'TBA'}
                    </td>
                    <td style="padding: 12px; color: #333;">
                        {schedule.location or 'Main Hall'}
                    </td>
                </tr>
                '''

                # Text version
                schedule_text += f'''
{time_range} - {schedule.title}
  Speaker: {schedule.speaker or 'TBA'}
  Location: {schedule.location or 'Main Hall'}
  {schedule.description if schedule.description else ''}

'''
        else:
            schedule_rows = '''
            <tr>
                <td colspan="4" style="padding: 20px; text-align: center; color: #666; font-style: italic;">
                    Schedule details will be updated soon.
                </td>
            </tr>
            '''
            schedule_text = "Schedule details will be updated soon."

        context = {
            'event_name': event.name,
            'event_date': event.start_date.strftime('%B %d, %Y'),
            'current_date': timezone.now().strftime('%B %d, %Y at %I:%M %p'),
            'schedule_rows': schedule_rows,
            'schedule_text': schedule_text,
        }
        
        success_count = 0
        for participant in participants:
            participant_context = context.copy()
            participant_context['participant_name'] = participant.full_name
            participant_context.update(self._get_contact_person_context(participant))

            if self.send_email(
                'schedule_update',
                participant.email,
                participant.full_name,
                participant_context
            ):
                success_count += 1
        
        return success_count
    
    def send_daily_gallery(self, participants, event, date=None):
        """Send daily event gallery images in a zip file"""
        if not date:
            date = timezone.now().date()
        
        # Get gallery images for the specific date
        gallery_images = EventGallery.objects.filter(
            event=event,
            uploaded_at__date=date
        )
        
        if not gallery_images.exists():
            print(f"No gallery images found for {date}")
            return 0
        
        # Create zip file with images
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
            with zipfile.ZipFile(temp_zip.name, 'w') as zip_file:
                for image in gallery_images:
                    if os.path.exists(image.image.path):
                        # Add image to zip with a clean filename
                        filename = f"{image.title}_{image.id}.jpg"
                        zip_file.write(image.image.path, filename)
            
            zip_path = temp_zip.name
        
        context = {
            'event_name': event.name,
            'date': date.strftime('%B %d, %Y'),
            'image_count': gallery_images.count(),
        }
        
        success_count = 0
        for participant in participants:
            participant_context = context.copy()
            participant_context['participant_name'] = participant.full_name
            participant_context.update(self._get_contact_person_context(participant))

            if self.send_email(
                'daily_gallery',
                participant.email,
                participant.full_name,
                participant_context,
                [zip_path]
            ):
                success_count += 1
        
        # Clean up temporary zip file
        try:
            os.unlink(zip_path)
        except OSError:
            pass
        
        return success_count
    
    def send_event_reminder(self, participants, event, days_before=1):
        """Send event reminder to participants"""
        context = {
            'event_name': event.name,
            'event_date': event.start_date.strftime('%B %d, %Y at %I:%M %p'),
            'event_location': event.location,
            'days_before': days_before,
        }
        
        success_count = 0
        for participant in participants:
            participant_context = context.copy()
            participant_context['participant_name'] = participant.full_name
            participant_context.update(self._get_contact_person_context(participant))

            if self.send_email(
                'event_reminder',
                participant.email,
                participant.full_name,
                participant_context
            ):
                success_count += 1
        
        return success_count

    def _build_hotel_section(self, participant):
        """Build hotel assignment section HTML with enhanced design"""
        if not participant.assigned_hotel:
            return '''
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #ffc107;">
                <h3 style="color: #ffc107; margin-top: 0; font-size: 18px;">🏨 Hotel Assignment</h3>
                <div style="background: #fff3cd; border: 1px solid #ffc107; border-radius: 8px; padding: 20px; text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 15px;">🏨</div>
                    <p style="margin: 0; font-weight: bold; color: #856404;">No hotel assigned yet.</p>
                    <p style="margin: 10px 0 0 0; color: #856404;">Hotel assignments will be communicated separately if applicable to your participation type.</p>
                </div>
            </div>
            '''

        hotel = participant.assigned_hotel
        room_info = f"Room {participant.assigned_room.room_number}" if participant.assigned_room else "To be assigned at check-in"

        # Hotel rating stars
        star_rating = ""
        if hasattr(hotel, 'star_rating') and hotel.star_rating:
            star_rating = "⭐" * int(hotel.star_rating)

        return f'''
        <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #28a745;">
            <h3 style="color: #28a745; margin-top: 0; font-size: 18px;">🏨 Your Hotel Assignment</h3>

            <!-- Hotel Header -->
            <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
                <div style="font-size: 48px; margin-bottom: 10px;">🏨</div>
                <h2 style="margin: 0; font-size: 24px;">{hotel.name}</h2>
                {f'<div style="margin-top: 5px; font-size: 18px;">{star_rating}</div>' if star_rating else ''}
                <div style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; display: inline-block; margin-top: 10px; font-weight: bold;">
                    ✅ Confirmed Reservation
                </div>
            </div>

            <!-- Hotel Details Grid -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <div style="font-weight: bold; color: #28a745; margin-bottom: 5px;">📍 Address</div>
                    <div style="color: #333;">{hotel.address}</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                    <div style="font-weight: bold; color: #17a2b8; margin-bottom: 5px;">📞 Phone</div>
                    <div style="color: #333;">{hotel.phone}</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6f42c1;">
                    <div style="font-weight: bold; color: #6f42c1; margin-bottom: 5px;">🏠 Room Assignment</div>
                    <div style="color: #333; font-weight: bold;">{room_info}</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #fd7e14;">
                    <div style="font-weight: bold; color: #fd7e14; margin-bottom: 5px;">👤 Contact Person</div>
                    <div style="color: #333;">{getattr(hotel, 'contact_person', 'Front Desk')}</div>
                </div>
            </div>

            <!-- Check-in Instructions -->
            <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 20px;">
                <h4 style="color: #0c5460; margin-top: 0; margin-bottom: 15px;">📋 Check-in Instructions</h4>
                <ul style="color: #0c5460; margin: 0; padding-left: 20px; line-height: 1.6;">
                    <li><strong>Check-in Time:</strong> 3:00 PM (15:00)</li>
                    <li><strong>Check-out Time:</strong> 12:00 PM (12:00)</li>
                    <li><strong>Required Documents:</strong> Event badge and valid ID</li>
                    <li><strong>Early Check-in:</strong> Subject to availability</li>
                    <li><strong>Luggage Storage:</strong> Available at front desk</li>
                    <li><strong>WiFi:</strong> Complimentary high-speed internet</li>
                </ul>
            </div>
        </div>
        '''

    def _build_driver_section(self, participant):
        """Build driver assignment section HTML with photos and badges"""
        if not participant.assigned_driver:
            return '''
            <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #ffc107;">
                <h3 style="color: #ffc107; margin-top: 0; font-size: 18px;">🚗 Transportation</h3>
                <div style="background: #fff3cd; border: 1px solid #ffc107; border-radius: 8px; padding: 20px; text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 15px;">🚗</div>
                    <p style="margin: 0; font-weight: bold; color: #856404;">No driver assigned.</p>
                    <p style="margin: 10px 0 0 0; color: #856404;">You may arrange your own transportation to the event venue. Parking is available on campus.</p>
                </div>
            </div>
            '''

        driver = participant.assigned_driver

        # Get driver photo URL if available
        driver_photo_html = ""
        if driver.photo:
            try:
                from django.conf import settings
                photo_url = f"{settings.MEDIA_URL}{driver.photo.name}"
                driver_photo_html = f'''
                <div style="text-align: center; margin-bottom: 15px;">
                    <img src="{photo_url}" alt="{driver.name}"
                         style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 3px solid #007bff; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                </div>
                '''
            except:
                driver_photo_html = '''
                <div style="text-align: center; margin-bottom: 15px;">
                    <div style="width: 80px; height: 80px; border-radius: 50%; background: #e9ecef; border: 3px solid #007bff; display: inline-flex; align-items: center; justify-content: center; font-size: 24px;">
                        👤
                    </div>
                </div>
                '''
        else:
            driver_photo_html = '''
            <div style="text-align: center; margin-bottom: 15px;">
                <div style="width: 80px; height: 80px; border-radius: 50%; background: #e9ecef; border: 3px solid #007bff; display: inline-flex; align-items: center; justify-content: center; font-size: 24px; margin: 0 auto;">
                    👤
                </div>
            </div>
            '''

        # Get driver badge if available
        driver_badge_html = ""
        try:
            from badges.models import DriverBadge
            driver_badge = DriverBadge.objects.get(driver=driver)
            if driver_badge.badge_image:
                from django.conf import settings
                badge_url = f"{settings.MEDIA_URL}{driver_badge.badge_image.name}"
                driver_badge_html = f'''
                <div style="text-align: center; margin-top: 15px;">
                    <p style="margin: 5px 0; font-weight: bold; color: #007bff;">📱 Driver Badge (for verification)</p>
                    <img src="{badge_url}" alt="Driver Badge"
                         style="max-width: 200px; height: auto; border-radius: 8px; border: 2px solid #007bff; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                </div>
                '''
        except:
            pass  # No badge available

        return f'''
        <div style="background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-top: 4px solid #007bff;">
            <h3 style="color: #007bff; margin-top: 0; font-size: 18px;">🚗 Your Transportation Assignment</h3>

            <!-- Driver Header with Photo -->
            <div style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
                {driver_photo_html}
                <h2 style="margin: 0; font-size: 24px;">{driver.name}</h2>
                <div style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; display: inline-block; margin-top: 10px; font-weight: bold;">
                    🚗 Your Assigned Driver
                </div>
            </div>

            <!-- Driver & Vehicle Details Grid -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <div style="font-weight: bold; color: #28a745; margin-bottom: 5px;">📞 Phone Number</div>
                    <div style="color: #333; font-weight: bold; font-size: 16px;">{driver.phone}</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;">
                    <div style="font-weight: bold; color: #dc3545; margin-bottom: 5px;">📧 Email</div>
                    <div style="color: #333;">{driver.email}</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6f42c1;">
                    <div style="font-weight: bold; color: #6f42c1; margin-bottom: 5px;">🚙 Vehicle</div>
                    <div style="color: #333;">{driver.car_model} {f"({driver.car_color})" if driver.car_color else ""}</div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #fd7e14;">
                    <div style="font-weight: bold; color: #fd7e14; margin-bottom: 5px;">🔢 License Plate</div>
                    <div style="color: #333; font-weight: bold; font-size: 16px;">{driver.car_plate}</div>
                </div>
            </div>

            <!-- Transportation Instructions -->
            <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 20px;">
                <h4 style="color: #0c5460; margin-top: 0; margin-bottom: 15px;">📋 Transportation Instructions</h4>
                <ul style="color: #0c5460; margin: 0; padding-left: 20px; line-height: 1.6;">
                    <li><strong>Contact Timeline:</strong> Your driver will contact you 24 hours before the event</li>
                    <li><strong>Pickup Arrangement:</strong> Pickup time and location will be coordinated via phone</li>
                    <li><strong>Phone Accessibility:</strong> Please keep your phone accessible for coordination</li>
                    <li><strong>Identification:</strong> Driver will show their badge for verification</li>
                    <li><strong>Emergency Contact:</strong> Save the driver's number in your phone</li>
                    <li><strong>Punctuality:</strong> Please be ready 5 minutes before scheduled pickup</li>
                </ul>
            </div>

            {driver_badge_html}
        </div>
        '''

    def _build_contact_person_section(self, participant):
        """Build contact person assignment section HTML"""
        if not participant.assigned_contact_person:
            return '''
            <div class="section">
                <div class="section-header">👤 Contact Person</div>
                <div class="assignment-card" style="background: #fff3cd; border-color: #ffc107;">
                    <p><strong>No specific contact person assigned.</strong></p>
                    <p>For any assistance, please contact the main event organizer listed above.</p>
                </div>
            </div>
            '''

        contact = participant.assigned_contact_person

        return f'''
        <div class="section">
            <div class="section-header">👤 Your Contact Person</div>
            <div class="assignment-card">
                <div class="assignment-header">
                    <i>👤</i> Your Dedicated Contact
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Name</div>
                        <div class="info-value">{contact.full_name}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Position</div>
                        <div class="info-value">{contact.position}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Phone</div>
                        <div class="info-value">{contact.phone}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Email</div>
                        <div class="info-value">{contact.email}</div>
                    </div>
                </div>
                <div class="info-item" style="grid-column: 1 / -1; margin-top: 15px;">
                    <div class="info-label">Contact Instructions</div>
                    <div class="info-value">This person is your primary contact for any questions or assistance during the event. Feel free to reach out for help with logistics, directions, or any other needs.</div>
                </div>
            </div>
        </div>
        '''


# Convenience function for easy import
def get_email_service():
    """Get an instance of EmailService"""
    return EmailService()
