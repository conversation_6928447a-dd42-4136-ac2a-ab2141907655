from rest_framework import serializers
from .models import Badge, BadgeTemplate, DriverBadge, ContactPersonBadge


class BadgeTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = BadgeTemplate
        fields = '__all__'


class BadgeSerializer(serializers.ModelSerializer):
    participant_name = serializers.CharField(source='participant.full_name', read_only=True)
    participant_email = serializers.CharField(source='participant.email', read_only=True)
    participant_type = serializers.CharField(source='participant.participant_type.name', read_only=True)
    participant_type_color = serializers.CharField(source='participant.participant_type.color', read_only=True)
    event_name = serializers.CharField(source='participant.event.name', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)

    class Meta:
        model = Badge
        fields = '__all__'


class DriverBadgeSerializer(serializers.ModelSerializer):
    driver_name = serializers.Char<PERSON><PERSON>(source='driver.name', read_only=True)
    driver_email = serializers.Char<PERSON>ield(source='driver.email', read_only=True)
    driver_phone = serializers.CharField(source='driver.phone', read_only=True)
    car_plate = serializers.Char<PERSON>ield(source='driver.car_plate', read_only=True)
    car_model = serializers.CharField(source='driver.car_model', read_only=True)
    event_name = serializers.CharField(source='driver.event.name', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)

    class Meta:
        model = DriverBadge
        fields = '__all__'


class ContactPersonBadgeSerializer(serializers.ModelSerializer):
    contact_person_name = serializers.CharField(source='contact_person.full_name', read_only=True)
    contact_person_email = serializers.CharField(source='contact_person.email', read_only=True)
    contact_person_phone = serializers.CharField(source='contact_person.phone', read_only=True)
    position = serializers.CharField(source='contact_person.position', read_only=True)
    organization = serializers.CharField(source='contact_person.organization', read_only=True)
    event_name = serializers.CharField(source='contact_person.event.name', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)

    class Meta:
        model = ContactPersonBadge
        fields = '__all__'
