version: '3.8'

services:
  # PostgreSQL Database for Production
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    env_file:
      - .env
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-uog_event_prod}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
    networks:
      - backend
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery and Caching
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    env_file:
      - .env
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_uog_2024}
    volumes:
      - redis_prod_data:/data
    networks:
      - backend
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "-a", "${REDIS_PASSWORD:-redis_uog_2024}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # pgAdmin for Database Management
  pgadmin:
    image: dpage/pgadmin4:latest
    restart: unless-stopped
    env_file:
      - .env
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD:-admin123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - backend
    depends_on:
      db:
        condition: service_healthy

  # Django Backend (Production)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: unless-stopped
    env_file:
      - .env
    environment:
      # Override .env values for Docker-specific settings
      - DEBUG=False
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,backend,************,nginx,event.uog.edu.et,www.event.uog.edu.et
      - CORS_ALLOWED_ORIGINS=http://localhost,http://127.0.0.1,http://************,https://************,https://event.uog.edu.et,https://www.event.uog.edu.et
      - CSRF_TRUSTED_ORIGINS=http://localhost,http://127.0.0.1,http://************,https://************,https://event.uog.edu.et,https://www.event.uog.edu.et
      - USE_TZ=True
      - SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
      - SECURE_SSL_REDIRECT=True
      - SESSION_COOKIE_SECURE=True
      - CSRF_COOKIE_SECURE=True
      - SECURE_BROWSER_XSS_FILTER=True
      - SECURE_CONTENT_TYPE_NOSNIFF=True
      - SECURE_HSTS_SECONDS=31536000
      - SECURE_HSTS_INCLUDE_SUBDOMAINS=True
      - SECURE_HSTS_PRELOAD=True
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_uog_2024}@redis:6379/0
      - MEDIA_URL=/media/
      - STATIC_URL=/static/
      - MEDIA_ROOT=/app/media
      - STATIC_ROOT=/app/staticfiles
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-redis_uog_2024}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-redis_uog_2024}@redis:6379/0
      - HEALTH_CHECK_URL=/health/
    # healthcheck:
    #   test: ["CMD", "python", "/app/healthcheck.py"]
    #   interval: 10s
    #   timeout: 5s
    #   retries: 5
    #   start_period: 60s
    volumes:
      - ./backend:/app
      - media_prod_files:/app/media
      - static_prod_files:/app/staticfiles
      - ./logs:/app/logs
    networks:
      - backend
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    # healthcheck:
    #   test: ["CMD", "python", "/app/healthcheck.py"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3

  # Celery Worker (Production)
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: unless-stopped
    env_file:
      - .env
    environment:
      # Override .env values for Docker-specific settings
      - DEBUG=False
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_uog_2024}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-redis_uog_2024}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-redis_uog_2024}@redis:6379/0
    volumes:
      - ./backend:/app
      - media_prod_files:/app/media
      - static_prod_files:/app/staticfiles
      - ./logs:/app/logs
    networks:
      - backend
    depends_on:
      - db
      - redis
      - backend
    entrypoint: ["celery-entrypoint.sh"]
    command: celery -A event_management worker --loglevel=info --concurrency=4

  # React Frontend (Production Build)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        REACT_APP_API_BASE_URL: ${REACT_APP_API_BASE_URL:-/api}
        REACT_APP_BACKEND_URL: ${REACT_APP_BACKEND_URL:-}
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - frontend_build:/usr/share/nginx/html
    networks:
      - frontend
    depends_on:
      - backend

  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    restart: unless-stopped
    env_file:
      - .env
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - frontend_build:/usr/share/nginx/html:ro
      - static_prod_files:/var/www/static:ro
      - media_prod_files:/var/www/media:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
      - certbot_certs:/etc/letsencrypt:ro
      - certbot_www:/var/www/certbot
    networks:
      - frontend
      - backend
    depends_on:
      - backend
      - frontend
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost/health/"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3

  # Certbot for Let's Encrypt SSL certificates
  certbot:
    image: certbot/certbot:latest
    restart: "no"
    volumes:
      - certbot_certs:/etc/letsencrypt
      - certbot_www:/var/www/certbot
      - ./logs/certbot:/var/log/letsencrypt
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"

volumes:
  postgres_prod_data:
  redis_prod_data:
  pgadmin_data:
  media_prod_files:
  static_prod_files:
  frontend_build:
  certbot_certs:
  certbot_www:

networks:
  frontend:
    name: uog-event-frontend
  backend:
    name: uog-event-backend
