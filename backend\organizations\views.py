from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .models import Organization, OrganizationSettings
from .serializers import (
    OrganizationListSerializer,
    OrganizationDetailSerializer,
    OrganizationCreateUpdateSerializer,
    OrganizationSettingsSerializer
)


class OrganizationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing organizations"""
    
    queryset = Organization.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'list':
            return OrganizationListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return OrganizationCreateUpdateSerializer
        return OrganizationDetailSerializer
    
    def get_queryset(self):
        """Filter queryset based on user permissions"""
        queryset = Organization.objects.all()
        
        # Filter by active status if requested
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        return queryset.order_by('-is_primary', 'name')
    
    def perform_create(self, serializer):
        """Create organization with default settings"""
        organization = serializer.save()
        
        # Create default settings for the organization
        OrganizationSettings.objects.create(organization=organization)
    
    @action(detail=True, methods=['get', 'put', 'patch'])
    def organization_settings(self, request, pk=None):
        """Manage organization settings"""
        organization = self.get_object()
        settings, created = OrganizationSettings.objects.get_or_create(
            organization=organization
        )
        
        if request.method == 'GET':
            serializer = OrganizationSettingsSerializer(settings)
            return Response(serializer.data)
        
        elif request.method in ['PUT', 'PATCH']:
            partial = request.method == 'PATCH'
            serializer = OrganizationSettingsSerializer(
                settings, 
                data=request.data, 
                partial=partial
            )
            
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def set_primary(self, request, pk=None):
        """Set this organization as the primary organization"""
        organization = self.get_object()
        
        # Remove primary status from all other organizations
        Organization.objects.filter(is_primary=True).update(is_primary=False)
        
        # Set this organization as primary
        organization.is_primary = True
        organization.save()
        
        serializer = self.get_serializer(organization)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def primary(self, request):
        """Get the primary organization"""
        try:
            primary_org = Organization.objects.get(is_primary=True)
            serializer = self.get_serializer(primary_org)
            return Response(serializer.data)
        except Organization.DoesNotExist:
            return Response(
                {'detail': 'No primary organization found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """Toggle organization active status"""
        organization = self.get_object()
        organization.is_active = not organization.is_active
        organization.save()
        
        serializer = self.get_serializer(organization)
        return Response(serializer.data)
    
    def destroy(self, request, *args, **kwargs):
        """Custom delete with validation"""
        organization = self.get_object()
        
        # Prevent deletion of primary organization
        if organization.is_primary:
            return Response(
                {'detail': 'Cannot delete the primary organization'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if organization has associated events
        if hasattr(organization, 'events') and organization.events.exists():
            return Response(
                {'detail': 'Cannot delete organization with associated events'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def get_primary_organization(request):
    """Get the primary organization for branding purposes"""
    try:
        organization = Organization.objects.filter(is_primary=True).first()
        if organization:
            serializer = OrganizationDetailSerializer(organization)
            return Response(serializer.data)
        else:
            # Return default University of Gondar data if no primary organization exists
            return Response({
                'id': None,
                'name': 'University of Gondar',
                'short_name': 'UoG',
                'motto': 'Excellence in Education, Research and Community Service',
                'logo': None,
                'website': 'https://www.uog.edu.et',
                'email': '<EMAIL>',
                'phone': '+251-58-114-1240',
                'full_address': 'Gondar, Amhara Region, Ethiopia',
                'is_primary': True
            })
    except Exception as e:
        return Response(
            {'error': 'Failed to fetch organization data'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
