from django.core.management.base import BaseCommand
from badges.models import BadgeTemplate


class Command(BaseCommand):
    help = 'Create professional badge templates for University of Gondar'
    
    def handle(self, *args, **options):
        # Create professional badge templates (90mm × 140mm)
        templates = [
            {
                'name': 'UoG Professional SVG Template',
                'description': 'Professional badge template based on SVG design (90mm × 140mm)',
                'width': 1063,  # 90mm width at 300 DPI
                'height': 1654,  # 140mm height at 300 DPI
                'background_color': '#f8f9fa',
                'is_default': True
            },
            {
                'name': 'UoG Compact Legacy',
                'description': 'Legacy compact badge template for compatibility',
                'width': 400,
                'height': 600,
                'background_color': '#ffffff',
                'is_default': False
            },
            {
                'name': 'UoG Premium SVG Template',
                'description': 'Premium badge template based on SVG design with enhanced elements',
                'width': 1063,
                'height': 1654,
                'background_color': '#f8f9fa',
                'is_default': False
            },
            {
                'name': 'UoG VIP SVG Template',
                'description': 'Special VIP badge template based on SVG design with gold accents',
                'width': 1063,
                'height': 1654,
                'background_color': '#fffef7',
                'is_default': False
            },
            {
                'name': 'UoG Conference SVG Template',
                'description': 'Conference-specific badge template based on SVG design',
                'width': 1063,
                'height': 1654,
                'background_color': '#f8f9fa',
                'is_default': False
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for template_data in templates:
            template, created = BadgeTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Created badge template: {template.name}')
                )
            else:
                # Update existing template
                for key, value in template_data.items():
                    setattr(template, key, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'↻ Updated badge template: {template.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n🎉 Badge template setup completed!'
                f'\n📊 Created: {created_count} templates'
                f'\n📊 Updated: {updated_count} templates'
                f'\n\n🎨 Available Templates:'
            )
        )
        
        # List all templates
        for template in BadgeTemplate.objects.all():
            status = '🟢 DEFAULT' if template.is_default else '🔵 AVAILABLE'
            self.stdout.write(
                f'   {status} {template.name} ({template.width}x{template.height})'
            )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n💡 Templates are ready for badge generation!'
                f'\n📝 You can now generate professional badges for participants.'
            )
        )
