# University of Gondar Event Management System
# Let's Encrypt SSL Certificate Initialization Script (PowerShell)
# This script safely initializes SSL certificates for event.uog.edu.et

param(
    [string]$Domain = "event.uog.edu.et",
    [string]$Email = "<EMAIL>",
    [switch]$Staging = $false
)

# Configuration
$ErrorActionPreference = "Stop"

Write-Host "🔐 University of Gondar Event Management System" -ForegroundColor Blue
Write-Host "🚀 Let's Encrypt SSL Certificate Initialization" -ForegroundColor Blue
Write-Host "================================================" -ForegroundColor Blue

# Check if domain is accessible
Write-Host "`n🔍 Checking domain accessibility..." -ForegroundColor Yellow
try {
    $ping = Test-Connection -ComputerName $Domain -Count 1 -Quiet
    if (-not $ping) {
        throw "Domain not accessible"
    }
    Write-Host "✅ Domain $Domain is accessible" -ForegroundColor Green
} catch {
    Write-Host "❌ Error: Domain $Domain is not accessible from this server" -ForegroundColor Red
    Write-Host "   Please ensure:" -ForegroundColor Red
    Write-Host "   1. DNS A record points to this server's IP" -ForegroundColor Red
    Write-Host "   2. Firewall allows HTTP (port 80) and HTTPS (port 443)" -ForegroundColor Red
    Write-Host "   3. Domain is properly configured" -ForegroundColor Red
    exit 1
}

# Check if certificates already exist
if (Test-Path "./certbot/conf/live/$Domain") {
    Write-Host "`n⚠️  SSL certificates for $Domain already exist" -ForegroundColor Yellow
    $response = Read-Host "Do you want to renew them? (y/N)"
    if ($response -ne "y" -and $response -ne "Y") {
        Write-Host "ℹ️  Skipping certificate generation" -ForegroundColor Blue
        exit 0
    }
}

# Create necessary directories
Write-Host "`n📁 Creating certificate directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./certbot/conf" | Out-Null
New-Item -ItemType Directory -Force -Path "./certbot/www" | Out-Null
New-Item -ItemType Directory -Force -Path "./logs/certbot" | Out-Null

# Stop any running containers
Write-Host "`n🛑 Stopping existing containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.prod.yml down

# Create temporary nginx config for certificate generation
Write-Host "`n⚙️  Creating temporary nginx configuration..." -ForegroundColor Yellow
$tempConfig = @"
# Temporary configuration for SSL certificate generation
server {
    listen 80;
    server_name $Domain www.$Domain;

    # Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files `$uri =404;
    }

    # Temporary location for other requests during setup
    location / {
        return 200 'SSL certificate generation in progress...';
        add_header Content-Type text/plain;
    }
}
"@

$tempConfig | Out-File -FilePath "./nginx/conf.d/temp-ssl.conf" -Encoding UTF8

# Backup original config
if (Test-Path "./nginx/conf.d/default.conf") {
    Copy-Item "./nginx/conf.d/default.conf" "./nginx/conf.d/default.conf.backup"
    Write-Host "✅ Backed up original nginx configuration" -ForegroundColor Green
}

# Temporarily disable SSL config
Move-Item "./nginx/conf.d/default.conf" "./nginx/conf.d/default.conf.ssl"

# Start nginx with temporary config
Write-Host "`n🚀 Starting nginx with temporary configuration..." -ForegroundColor Yellow
docker-compose -f docker-compose.prod.yml up -d nginx

# Wait for nginx to be ready
Write-Host "⏳ Waiting for nginx to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Generate SSL certificate
Write-Host "`n🔐 Generating SSL certificate for $Domain..." -ForegroundColor Yellow

$stagingArg = ""
if ($Staging) {
    Write-Host "⚠️  Using Let's Encrypt STAGING environment (for testing)" -ForegroundColor Yellow
    $stagingArg = "--staging"
} else {
    Write-Host "🔥 Using Let's Encrypt PRODUCTION environment" -ForegroundColor Green
}

# Run certbot
$certbotCmd = "docker-compose -f docker-compose.prod.yml run --rm certbot certonly --webroot --webroot-path=/var/www/certbot --email $Email --agree-tos --no-eff-email --force-renewal $stagingArg -d $Domain -d www.$Domain"
Invoke-Expression $certbotCmd

# Check if certificate was generated successfully
if (-not (Test-Path "./certbot/conf/live/$Domain/fullchain.pem")) {
    Write-Host "❌ Error: SSL certificate generation failed" -ForegroundColor Red
    Write-Host "   Check the logs above for details" -ForegroundColor Red
    
    # Restore original config
    if (Test-Path "./nginx/conf.d/default.conf.backup") {
        Move-Item "./nginx/conf.d/default.conf.backup" "./nginx/conf.d/default.conf"
    }
    Remove-Item "./nginx/conf.d/temp-ssl.conf" -ErrorAction SilentlyContinue
    
    exit 1
}

Write-Host "✅ SSL certificate generated successfully!" -ForegroundColor Green

# Restore SSL-enabled nginx configuration
Write-Host "`n⚙️  Restoring SSL-enabled nginx configuration..." -ForegroundColor Yellow
Move-Item "./nginx/conf.d/default.conf.ssl" "./nginx/conf.d/default.conf"
Remove-Item "./nginx/conf.d/temp-ssl.conf" -ErrorAction SilentlyContinue

# Stop containers
docker-compose -f docker-compose.prod.yml down

# Start all services with SSL
Write-Host "`n🚀 Starting all services with SSL enabled..." -ForegroundColor Yellow
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be ready
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Test HTTPS connection
Write-Host "`n🧪 Testing HTTPS connection..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://$Domain/health/" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ HTTPS is working correctly!" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  HTTPS test failed, but this might be normal during startup" -ForegroundColor Yellow
    Write-Host "   Please wait a few minutes and test manually" -ForegroundColor Yellow
}

# Display certificate information
Write-Host "`n📋 Certificate Information:" -ForegroundColor Blue
docker-compose -f docker-compose.prod.yml run --rm certbot certificates

Write-Host "`n🎉 SSL setup completed successfully!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host "✅ Your University of Gondar Event Management System is now secured with SSL" -ForegroundColor Green
Write-Host "🌐 Access your application at: https://$Domain" -ForegroundColor Green
Write-Host "🔒 SSL certificate will auto-renew every 12 hours" -ForegroundColor Green
Write-Host "📧 Certificate notifications will be sent to: $Email" -ForegroundColor Green
Write-Host "`nℹ️  Important Notes:" -ForegroundColor Blue
Write-Host "   • Certificates are stored in ./certbot/conf/live/$Domain/" -ForegroundColor Blue
Write-Host "   • Auto-renewal is configured via the certbot container" -ForegroundColor Blue
Write-Host "   • Monitor logs in ./logs/certbot/ for renewal status" -ForegroundColor Blue
Write-Host "   • Update your DNS if you change server IP" -ForegroundColor Blue

# Clean up backup
if (Test-Path "./nginx/conf.d/default.conf.backup") {
    Remove-Item "./nginx/conf.d/default.conf.backup"
}

Write-Host "`n🔐 SSL initialization complete! 🔐" -ForegroundColor Green
