/* Custom styles for Event Management System */

.App {
  min-height: 100vh;
}

/* Hover effects */
.hover-shadow {
  transition: box-shadow 0.3s ease-in-out;
}

.hover-shadow:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Custom button styles */
.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Card styles */
.card {
  border-radius: 1rem;
  border: none;
  transition: all 0.3s ease;
}

.card-header {
  border-radius: 1rem 1rem 0 0 !important;
  border-bottom: none;
  font-weight: 600;
}

/* Form styles */
.form-control, .form-select {
  border-radius: 0.5rem;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Badge styles */
.badge {
  font-weight: 500;
  padding: 0.5em 0.75em;
}

/* Navbar styles */
.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

/* Hero section */
.bg-primary {
  background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
}

/* Loading spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Responsive images */
.card-img-top {
  transition: transform 0.3s ease;
}

.card:hover .card-img-top {
  transform: scale(1.05);
}

/* Date picker styles */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
  padding: 0.375rem 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.react-datepicker__input-container input:focus {
  border-color: #0d6efd;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Alert styles */
.alert {
  border-radius: 0.75rem;
  border: none;
}
