import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useBranding } from '../hooks/useBranding';

interface LandingNavbarProps {
  useHomeNavigation?: boolean;
}

const LandingNavbar: React.FC<LandingNavbarProps> = ({ useHomeNavigation = false }) => {
  const location = useLocation();
  const { organization } = useBranding();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const navigationSections = [
    { id: 'gallery', label: 'Gallery', icon: 'fas fa-images' },
    { id: 'attractions', label: 'Gondar', icon: 'fas fa-landmark' }
  ];

  return (
    <nav className="landing-nav position-fixed w-100 top-0 bg-white shadow-sm" style={{ zIndex: 1000 }}>
      <div className="modern-container">
        <div className="d-flex justify-content-between align-items-center py-2">
          <div className="d-flex align-items-center">
            <Link to="/" className="d-flex align-items-center text-decoration-none">
              {organization?.logo ? (
                <img
                  src={organization.logo}
                  alt={organization.name}
                  className="me-3"
                  style={{
                    height: '45px',
                    width: 'auto',
                    objectFit: 'contain'
                  }}
                />
              ) : (
                <span className="me-2" style={{ fontSize: '1.5rem' }}>🇪🇹</span>
              )}
              <div className="d-flex flex-column">
                <span className="fw-bold text-primary" style={{ fontSize: '1.1rem', lineHeight: '1.2' }}>
                  {organization?.short_name || 'UoG'} Events
                </span>
                <small className="text-muted" style={{ fontSize: '0.75rem', lineHeight: '1' }}>
                  {organization?.name || 'University of Gondar'}
                </small>
              </div>
            </Link>
          </div>
          {/* Desktop Navigation */}
          <div className="d-none d-lg-flex gap-4">
            {!useHomeNavigation ? (
              // Page-based navigation for standalone pages
              <>
                <Link
                  to="/"
                  className={`nav-link ${isActive('/') ? 'text-primary fw-bold' : 'text-dark'}`}
                >
                  <i className="fas fa-home me-2"></i>
                  Home
                </Link>
                <Link
                  to="/events"
                  className={`nav-link ${isActive('/events') ? 'text-primary fw-bold' : 'text-dark'}`}
                >
                  <i className="fas fa-calendar-alt me-2"></i>
                  Events
                </Link>
                <Link
                  to="/register"
                  className={`nav-link ${isActive('/register') ? 'text-primary fw-bold' : 'text-dark'}`}
                >
                  <i className="fas fa-user-plus me-2"></i>
                  Register
                </Link>
              </>
            ) : (
              // Show both page navigation AND section navigation for home page
              <>
                <Link
                  to="/"
                  className={`nav-link ${isActive('/') ? 'text-primary fw-bold' : 'text-dark'}`}
                >
                  <i className="fas fa-home me-2"></i>
                  Home
                </Link>
                <Link
                  to="/events"
                  className={`nav-link ${isActive('/events') ? 'text-primary fw-bold' : 'text-dark'}`}
                >
                  <i className="fas fa-calendar-alt me-2"></i>
                  Events
                </Link>
                <Link
                  to="/register"
                  className={`nav-link ${isActive('/register') ? 'text-primary fw-bold' : 'text-dark'}`}
                >
                  <i className="fas fa-user-plus me-2"></i>
                  Register
                </Link>
                {/* Section-based navigation for landing page style */}
                {navigationSections.map((section) => (
                <a
                  key={section.id}
                  href={`#${section.id}`}
                  className="nav-link text-dark"
                  onClick={(e) => {
                    if (location.pathname !== '/') {
                      // If not on home page, navigate to home first
                      window.location.href = `/#${section.id}`;
                    } else {
                      // If on home page, smooth scroll
                      e.preventDefault();
                      const element = document.getElementById(section.id);
                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth' });
                      }
                    }
                  }}
                >
                  <i className={`${section.icon} me-2`}></i>
                  {section.label}
                </a>
                ))}
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="btn btn-outline-primary d-lg-none"
            type="button"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle navigation"
          >
            <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'}`}></i>
          </button>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className="mobile-menu d-lg-none bg-white border-top">
            <div className="py-3">
              {!useHomeNavigation ? (
                // Page-based navigation for standalone pages
                <>
                  <Link
                    to="/"
                    className={`nav-link d-block py-2 px-3 ${isActive('/') ? 'text-primary fw-bold' : 'text-dark'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <i className="fas fa-home me-2"></i>
                    Home
                  </Link>
                  <Link
                    to="/events"
                    className={`nav-link d-block py-2 px-3 ${isActive('/events') ? 'text-primary fw-bold' : 'text-dark'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <i className="fas fa-calendar-alt me-2"></i>
                    Events
                  </Link>
                  <Link
                    to="/register"
                    className={`nav-link d-block py-2 px-3 ${isActive('/register') ? 'text-primary fw-bold' : 'text-dark'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <i className="fas fa-user-plus me-2"></i>
                    Register
                  </Link>
                </>
              ) : (
                // Show both page navigation AND section navigation for home page
                <>
                  <Link
                    to="/"
                    className={`nav-link d-block py-2 px-3 ${isActive('/') ? 'text-primary fw-bold' : 'text-dark'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <i className="fas fa-home me-2"></i>
                    Home
                  </Link>
                  <Link
                    to="/events"
                    className={`nav-link d-block py-2 px-3 ${isActive('/events') ? 'text-primary fw-bold' : 'text-dark'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <i className="fas fa-calendar-alt me-2"></i>
                    Events
                  </Link>
                  <Link
                    to="/register"
                    className={`nav-link d-block py-2 px-3 ${isActive('/register') ? 'text-primary fw-bold' : 'text-dark'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <i className="fas fa-user-plus me-2"></i>
                    Register
                  </Link>
                  {/* Section-based navigation for landing page style */}
                  {navigationSections.map((section) => (
                  <a
                    key={section.id}
                    href={`#${section.id}`}
                    className="nav-link d-block py-2 px-3 text-dark"
                    onClick={(e) => {
                      setIsMenuOpen(false);
                      if (location.pathname !== '/') {
                        // If not on home page, navigate to home first
                        window.location.href = `/#${section.id}`;
                      } else {
                        // If on home page, smooth scroll
                        e.preventDefault();
                        const element = document.getElementById(section.id);
                        if (element) {
                          element.scrollIntoView({ behavior: 'smooth' });
                        }
                      }
                    }}
                  >
                    <i className={`${section.icon} me-2`}></i>
                    {section.label}
                  </a>
                  ))}
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default LandingNavbar;
