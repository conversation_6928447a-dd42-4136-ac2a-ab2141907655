from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.http import HttpResponse
from django.utils import timezone
from django.contrib import messages
from django.shortcuts import redirect
import csv
import io
from .models import Participant, ParticipantType, Attendance, VisitingInterest, ParticipantVisitingInterest


@admin.register(ParticipantType)
class ParticipantTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'color', 'created_at']
    search_fields = ['name']


@admin.register(Participant)
class ParticipantAdmin(admin.ModelAdmin):
    list_display = [
        'full_name', 'email', 'institution_name', 'participant_type',
        'event', 'status', 'assigned_hotel', 'assigned_driver', 'assigned_contact_person',
        'badge_generated', 'details_email_sent'
    ]
    list_filter = [
        'status', 'participant_type', 'event', 'is_confirmed',
        'assigned_hotel', 'assigned_driver', 'assigned_contact_person',
        'badge_generated', 'details_email_sent', 'registration_date'
    ]
    search_fields = ['first_name', 'last_name', 'email', 'institution_name']
    readonly_fields = [
        'uuid', 'registration_date', 'approved_by', 'approved_at',
        'details_email_sent_at', 'created_at', 'updated_at'
    ]
    date_hierarchy = 'registration_date'

    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'middle_name', 'email', 'phone')
        }),
        ('Professional Information', {
            'fields': ('institution_name', 'position')
        }),
        ('Event Information', {
            'fields': ('event', 'participant_type', 'arrival_date', 'departure_date')
        }),
        ('Badge & Photo', {
            'fields': ('profile_photo', 'badge_generated', 'badge_file')
        }),
        ('Admin Assignment', {
            'fields': (
                'status', 'assigned_hotel', 'assigned_room', 'assigned_driver', 'assigned_contact_person',
                'admin_notes', 'rejection_reason'
            )
        }),
        ('Approval Information', {
            'fields': ('approved_by', 'approved_at', 'is_confirmed')
        }),
        ('Email Tracking', {
            'fields': ('details_email_sent', 'details_email_sent_at')
        }),
        ('System Information', {
            'fields': ('uuid', 'registration_date', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
        ('Additional Information', {
            'fields': ('remarks',),
            'classes': ('collapse',)
        })
    )

    actions = [
        'approve_participants', 'reject_participants', 'assign_hotels_drivers',
        'generate_badges', 'send_details_emails', 'export_to_csv'
    ]

    def approve_participants(self, request, queryset):
        """Approve selected participants"""
        updated = 0
        for participant in queryset.filter(status='pending'):
            participant.status = 'approved'
            participant.is_confirmed = True
            participant.approved_by = request.user
            participant.approved_at = timezone.now()
            participant.save()  # This triggers signals for email sending
            updated += 1
        self.message_user(request, f'{updated} participants approved successfully.')
    approve_participants.short_description = "Approve selected participants"

    def reject_participants(self, request, queryset):
        """Reject selected participants"""
        updated = 0
        for participant in queryset.filter(status='pending'):
            participant.status = 'rejected'
            participant.is_confirmed = False
            participant.save()  # This triggers signals for potential rejection email
            updated += 1
        self.message_user(request, f'{updated} participants rejected.')
    reject_participants.short_description = "Reject selected participants"

    def assign_hotels_drivers(self, request, queryset):
        """Mark participants as having hotel and driver assigned"""
        approved_participants = queryset.filter(status='approved')
        updated = 0
        for participant in approved_participants:
            if participant.assigned_hotel and participant.assigned_driver:
                participant.status = 'assigned'
                participant.save()
                updated += 1
        self.message_user(request, f'{updated} participants marked as assigned.')
    assign_hotels_drivers.short_description = "Mark as Hotel & Driver Assigned"

    def generate_badges(self, request, queryset):
        """Generate badges for assigned participants"""
        assigned_participants = queryset.filter(status='assigned')
        updated = 0
        for participant in assigned_participants:
            try:
                # Import here to avoid circular imports
                from badges.models import Badge
                badge, created = Badge.objects.get_or_create(participant=participant)
                badge.generate_badge()
                participant.badge_generated = True
                participant.status = 'badge_generated'
                participant.save()
                updated += 1
            except Exception as e:
                messages.error(request, f'Error generating badge for {participant.full_name}: {e}')
        self.message_user(request, f'{updated} badges generated successfully.')
    generate_badges.short_description = "Generate badges for assigned participants"

    def send_details_emails(self, request, queryset):
        """Send details emails to participants with badges"""
        participants_with_badges = queryset.filter(status='badge_generated', badge_generated=True)
        updated = 0
        for participant in participants_with_badges:
            try:
                # Import here to avoid circular imports
                from .utils import send_participant_details_email
                send_participant_details_email(participant)
                participant.details_email_sent = True
                participant.details_email_sent_at = timezone.now()
                participant.status = 'email_sent'
                participant.save()
                updated += 1
            except Exception as e:
                messages.error(request, f'Error sending email to {participant.full_name}: {e}')
        self.message_user(request, f'{updated} details emails sent successfully.')
    send_details_emails.short_description = "Send details emails with badges"

    def export_to_csv(self, request, queryset):
        """Export selected participants to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="participants.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Name', 'Email', 'Phone', 'Institution', 'Position', 'Event',
            'Participant Type', 'Status', 'Hotel', 'Room', 'Driver', 'Contact Person',
            'Arrival Date', 'Departure Date', 'Badge Generated', 'Email Sent'
        ])

        for participant in queryset:
            writer.writerow([
                participant.full_name,
                participant.email,
                participant.phone,
                participant.institution_name,
                participant.position,
                participant.event.name,
                participant.participant_type.name,
                participant.get_status_display(),
                participant.assigned_hotel.name if participant.assigned_hotel else '',
                participant.assigned_room.room_number if participant.assigned_room else '',
                participant.assigned_driver.name if participant.assigned_driver else '',
                participant.assigned_contact_person.full_name if participant.assigned_contact_person else '',
                participant.arrival_date,
                participant.departure_date,
                'Yes' if participant.badge_generated else 'No',
                'Yes' if participant.details_email_sent else 'No'
            ])

        return response
    export_to_csv.short_description = "Export selected participants to CSV"


@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ['participant', 'event_schedule', 'checked_in_at', 'checked_in_by']
    list_filter = ['event_schedule__event', 'checked_in_at']
    search_fields = ['participant__first_name', 'participant__last_name', 'checked_in_by']
    date_hierarchy = 'checked_in_at'


@admin.register(VisitingInterest)
class VisitingInterestAdmin(admin.ModelAdmin):
    list_display = ['name', 'event', 'location', 'current_participants_count', 'max_participants', 'is_active']
    list_filter = ['event', 'is_active', 'created_at']
    search_fields = ['name', 'description', 'location']
    readonly_fields = ['current_participants_count', 'created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'location', 'event')
        }),
        ('Settings', {
            'fields': ('is_active', 'max_participants')
        }),
        ('Statistics', {
            'fields': ('current_participants_count',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class ParticipantVisitingInterestInline(admin.TabularInline):
    model = ParticipantVisitingInterest
    extra = 0
    readonly_fields = ['selected_at']


@admin.register(ParticipantVisitingInterest)
class ParticipantVisitingInterestAdmin(admin.ModelAdmin):
    list_display = ['participant', 'visiting_interest', 'priority', 'selected_at']
    list_filter = ['visiting_interest__event', 'priority', 'selected_at']
    search_fields = ['participant__first_name', 'participant__last_name', 'visiting_interest__name']
    date_hierarchy = 'selected_at'
