from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.shortcuts import get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.db.models import Count, Q
from django.db import models
from datetime import datetime, timedelta
import csv
import io

from .models import Participant, ParticipantType, Attendance, VisitingInterest, ParticipantVisitingInterest
from .serializers import (
    ParticipantSerializer, ParticipantListSerializer,
    ParticipantRegistrationSerializer, ParticipantTypeSerializer,
    AttendanceSerializer, VisitingInterestSerializer, ParticipantVisitingInterestSerializer
)
from events.email_service import get_email_service


class ParticipantTypeViewSet(viewsets.ModelViewSet):
    queryset = ParticipantType.objects.all()
    serializer_class = ParticipantTypeSerializer

    def get_permissions(self):
        """Allow public access for list and retrieve actions"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]


class VisitingInterestViewSet(viewsets.ModelViewSet):
    queryset = VisitingInterest.objects.all()
    serializer_class = VisitingInterestSerializer

    def get_permissions(self):
        """Allow public access for list and retrieve actions"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)

        if event_id is not None:
            queryset = queryset.filter(event=event_id, is_active=True)

        return queryset.order_by('name')


class ParticipantViewSet(viewsets.ModelViewSet):
    queryset = Participant.objects.all()

    def get_permissions(self):
        """Allow public access for CRUD and assignment actions"""
        if self.action in ['create', 'list', 'retrieve', 'update', 'partial_update', 'destroy', 'confirm', 'approve', 'reject', 'assign_hotel', 'assign_driver', 'assign_contact_person', 'regenerate_badge', 'badge', 'export_csv', 'download_sample', 'download_sample_with_assignments', 'import_csv']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        if self.action == 'create':
            return ParticipantRegistrationSerializer
        elif self.action in ['update', 'partial_update']:
            return ParticipantRegistrationSerializer  # Use the same serializer for updates
        elif self.action == 'list':
            return ParticipantListSerializer
        return ParticipantSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)
        participant_type = self.request.query_params.get('type', None)

        if event_id is not None:
            queryset = queryset.filter(event=event_id)
        if participant_type is not None:
            queryset = queryset.filter(participant_type=participant_type)

        return queryset

    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """Confirm participant registration"""
        participant = self.get_object()
        participant.is_confirmed = True
        participant.save()

        serializer = self.get_serializer(participant)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve participant and send event details email"""
        participant = self.get_object()

        if participant.status == 'approved':
            return Response({'message': 'Participant is already approved'})

        # Store old status for signal
        old_status = participant.status

        # Update participant status
        participant.status = 'approved'
        participant.is_confirmed = True
        participant.approved_at = timezone.now()

        # Set approved_by if user is authenticated
        if request.user and request.user.is_authenticated:
            participant.approved_by = request.user

        participant.save()  # This triggers signals for email sending

        serializer = self.get_serializer(participant)
        return Response({
            'message': f'Participant {participant.full_name} approved successfully. Event details email will be sent.',
            'participant': serializer.data
        })

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject participant"""
        participant = self.get_object()

        if participant.status == 'rejected':
            return Response({'message': 'Participant is already rejected'})

        # Update participant status
        participant.status = 'rejected'
        participant.is_confirmed = False
        participant.save()

        serializer = self.get_serializer(participant)
        return Response({
            'message': f'Participant {participant.full_name} rejected.',
            'participant': serializer.data
        })

    @action(detail=True, methods=['post'])
    def assign_hotel(self, request, pk=None):
        """Assign or remove hotel assignment for participant"""
        participant = self.get_object()
        hotel_id = request.data.get('hotel_id')

        # Handle removal of assignment
        if not hotel_id or hotel_id == '0' or hotel_id == 0:
            participant.assigned_hotel = None
            participant.save()
            serializer = self.get_serializer(participant)
            return Response(serializer.data)

        try:
            from hotels.models import Hotel
            hotel = Hotel.objects.get(id=hotel_id)
            participant.assigned_hotel = hotel
            participant.save()

            serializer = self.get_serializer(participant)
            return Response(serializer.data)
        except Hotel.DoesNotExist:
            return Response({'error': 'Hotel not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def assign_driver(self, request, pk=None):
        """Assign or remove driver assignment for participant"""
        participant = self.get_object()
        driver_id = request.data.get('driver_id')

        # Handle removal of assignment
        if not driver_id or driver_id == '0' or driver_id == 0:
            participant.assigned_driver = None
            participant.save()
            serializer = self.get_serializer(participant)
            return Response(serializer.data)

        try:
            from drivers.models import Driver
            driver = Driver.objects.get(id=driver_id)
            participant.assigned_driver = driver
            participant.save()

            serializer = self.get_serializer(participant)
            return Response(serializer.data)
        except Driver.DoesNotExist:
            return Response({'error': 'Driver not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def assign_contact_person(self, request, pk=None):
        """Assign or remove contact person assignment for participant"""
        participant = self.get_object()
        contact_person_id = request.data.get('contact_person_id')

        # Handle removal of assignment
        if not contact_person_id or contact_person_id == '0' or contact_person_id == 0:
            participant.assigned_contact_person = None
            participant.save()
            serializer = self.get_serializer(participant)
            return Response(serializer.data)

        try:
            from contact_persons.models import ContactPerson
            contact_person = ContactPerson.objects.get(id=contact_person_id)
            participant.assigned_contact_person = contact_person
            participant.save()

            serializer = self.get_serializer(participant)
            return Response(serializer.data)
        except ContactPerson.DoesNotExist:
            return Response({'error': 'Contact person not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'])
    def badge(self, request, pk=None):
        """Get participant badge"""
        participant = self.get_object()
        if hasattr(participant, 'badge'):
            badge = participant.badge
            if badge.badge_image:
                return Response({
                    'badge_url': request.build_absolute_uri(badge.badge_image.url),
                    'qr_code_url': request.build_absolute_uri(badge.qr_code_image.url) if badge.qr_code_image else None,
                    'generated_at': badge.generated_at
                })
        return Response({'error': 'Badge not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def regenerate_badge(self, request, pk=None):
        """Regenerate participant badge"""
        participant = self.get_object()
        if hasattr(participant, 'badge'):
            badge = participant.badge
            try:
                badge.generate_badge()
                return Response({
                    'message': 'Badge regenerated successfully',
                    'badge_url': request.build_absolute_uri(badge.badge_image.url)
                })
            except Exception as e:
                return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response({'error': 'Badge not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def verify(self, request):
        """Verify participant by UUID (for QR code scanning)"""
        uuid = request.query_params.get('uuid')
        if not uuid:
            return Response({'error': 'UUID required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            participant = Participant.objects.get(uuid=uuid)
            serializer = ParticipantListSerializer(participant)
            return Response(serializer.data)
        except Participant.DoesNotExist:
            return Response({'error': 'Participant not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def export_csv(self, request):
        """Export participants to CSV"""
        event_id = request.query_params.get('event')

        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="participants_export.csv"'

        writer = csv.writer(response)

        # Write header
        writer.writerow([
            'First Name', 'Last Name', 'Middle Name', 'Email', 'Phone',
            'Institution Name', 'Position', 'Event Name', 'Participant Type',
            'Arrival Date', 'Departure Date', 'Status', 'Assigned Hotel',
            'Assigned Driver', 'Assigned Contact Person', 'Admin Notes', 'Remarks'
        ])

        # Get participants
        queryset = self.get_queryset().select_related(
            'event', 'participant_type', 'assigned_hotel',
            'assigned_driver', 'assigned_contact_person'
        )

        if event_id:
            queryset = queryset.filter(event_id=event_id)

        # Write data rows
        for participant in queryset:
            writer.writerow([
                participant.first_name,
                participant.last_name,
                participant.middle_name or '',
                participant.email,
                participant.phone,
                participant.institution_name,
                participant.position,
                participant.event.name,
                participant.participant_type.name,
                participant.arrival_date.strftime('%Y-%m-%d %H:%M') if participant.arrival_date else '',
                participant.departure_date.strftime('%Y-%m-%d %H:%M') if participant.departure_date else '',
                participant.status,
                participant.assigned_hotel.name if participant.assigned_hotel else '',
                f"{participant.assigned_driver.first_name} {participant.assigned_driver.last_name}" if participant.assigned_driver else '',
                participant.assigned_contact_person.full_name if participant.assigned_contact_person else '',
                participant.admin_notes or '',
                participant.remarks or ''
            ])

        return response

    @action(detail=False, methods=['get'])
    def download_sample(self, request):
        """Download sample CSV for participant import"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="participants_sample.csv"'

        writer = csv.writer(response)

        # Write header
        writer.writerow([
            'First Name', 'Last Name', 'Middle Name', 'Email', 'Phone',
            'Institution Name', 'Position', 'Event Name', 'Participant Type',
            'Arrival Date', 'Departure Date', 'Assigned Hotel',
            'Assigned Driver', 'Assigned Contact Person', 'Remarks'
        ])

        # Write sample data
        writer.writerow([
            'John', 'Doe', 'Michael', '<EMAIL>', '+1234567890',
            'University of Example', 'Professor', 'Sample Event', 'Speaker',
            '2024-01-15 10:00', '2024-01-17 18:00', 'Grand Hotel',
            'Jane Smith', 'Dr. Alice Johnson', 'VIP participant'
        ])

        writer.writerow([
            'Jane', 'Smith', '', '<EMAIL>', '+0987654321',
            'Example Institute', 'Researcher', 'Sample Event', 'Participant',
            '2024-01-15 14:00', '2024-01-16 12:00', '', '', '', 'Regular participant'
        ])

        return response

    @action(detail=False, methods=['get'])
    def download_sample_with_assignments(self, request):
        """Download sample CSV with current available assignments"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="participants_sample_with_assignments.csv"'

        writer = csv.writer(response)

        # Write header
        writer.writerow([
            'First Name', 'Last Name', 'Middle Name', 'Email', 'Phone',
            'Institution Name', 'Position', 'Event Name', 'Participant Type',
            'Arrival Date', 'Departure Date', 'Assigned Hotel',
            'Assigned Driver', 'Assigned Contact Person', 'Remarks'
        ])

        # Get available options for sample
        try:
            from events.models import Event
            from hotels.models import Hotel
            from drivers.models import Driver
            from contact_persons.models import ContactPerson

            events = list(Event.objects.all()[:2])
            hotels = list(Hotel.objects.filter(is_available=True)[:3])
            drivers = list(Driver.objects.filter(is_available=True)[:3])
            contacts = list(ContactPerson.objects.filter(is_available=True)[:3])
            participant_types = list(ParticipantType.objects.all()[:3])

            # Write sample data with real options
            if events and participant_types:
                writer.writerow([
                    'John', 'Doe', 'Michael', '<EMAIL>', '+1234567890',
                    'University of Example', 'Professor', events[0].name, participant_types[0].name,
                    '2024-01-15 10:00', '2024-01-17 18:00',
                    hotels[0].name if hotels else 'Sample Hotel',
                    f"{drivers[0].first_name} {drivers[0].last_name}" if drivers else 'John Driver',
                    contacts[0].full_name if contacts else 'Jane Contact',
                    'Sample participant with assignments'
                ])
            else:
                # Fallback sample data
                writer.writerow([
                    'John', 'Doe', 'Michael', '<EMAIL>', '+1234567890',
                    'University of Example', 'Professor', 'Sample Event', 'Speaker',
                    '2024-01-15 10:00', '2024-01-17 18:00',
                    'Sample Hotel', 'John Driver', 'Jane Contact',
                    'Sample participant with assignments'
                ])

            # Add comment rows explaining available options
            writer.writerow([])
            writer.writerow(['# Available Events:'] + [event.name for event in events] if events else ['# No events found'])
            writer.writerow(['# Available Participant Types:'] + [pt.name for pt in participant_types] if participant_types else ['# No participant types found'])
            writer.writerow(['# Available Hotels:'] + [hotel.name for hotel in hotels] if hotels else ['# No hotels found'])
            writer.writerow(['# Available Drivers:'] + [f"{d.first_name} {d.last_name}" for d in drivers] if drivers else ['# No drivers found'])
            writer.writerow(['# Available Contact Persons:'] + [cp.full_name for cp in contacts] if contacts else ['# No contact persons found'])
        except Exception as e:
            # Fallback in case of any errors
            writer.writerow([
                'John', 'Doe', 'Michael', '<EMAIL>', '+1234567890',
                'University of Example', 'Professor', 'Sample Event', 'Speaker',
                '2024-01-15 10:00', '2024-01-17 18:00',
                'Sample Hotel', 'John Driver', 'Jane Contact',
                'Sample participant with assignments'
            ])

        return response

    @action(detail=False, methods=['get'])
    def download_excel_template(self, request):
        """Download Excel template with dropdown validation for hotels, drivers, and contact persons"""
        import openpyxl
        from openpyxl.worksheet.datavalidation import DataValidation
        from hotels.models import Hotel
        from drivers.models import Driver
        from contact_persons.models import ContactPerson
        from events.models import Event

        # Create workbook and worksheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Participants Template"

        # Define headers
        headers = [
            'First Name', 'Last Name', 'Middle Name', 'Email', 'Phone',
            'Institution Name', 'Position', 'Event Name', 'Participant Type',
            'Arrival Date', 'Departure Date', 'Assigned Hotel', 'Assigned Driver',
            'Assigned Contact Person', 'Remarks'
        ]

        # Add headers to first row
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = openpyxl.styles.Font(bold=True)
            cell.fill = openpyxl.styles.PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # Get data for dropdowns
        hotels = list(Hotel.objects.filter(is_active=True).values_list('name', flat=True))
        drivers = [f"{d.first_name} {d.last_name}" for d in Driver.objects.filter(is_active=True)]
        contacts = [f"{c.first_name} {c.last_name}" for c in ContactPerson.objects.filter(is_active=True)]
        events = list(Event.objects.filter(is_active=True).values_list('name', flat=True))
        participant_types = list(ParticipantType.objects.values_list('name', flat=True))

        # Create dropdown validation for hotels (column L - 12)
        if hotels:
            hotel_validation = DataValidation(type="list", formula1=f'"{",".join(hotels)}"')
            hotel_validation.error = 'Please select a valid hotel from the dropdown'
            hotel_validation.errorTitle = 'Invalid Hotel'
            ws.add_data_validation(hotel_validation)
            hotel_validation.add(f'L2:L1000')  # Apply to column L from row 2 to 1000

        # Create dropdown validation for drivers (column M - 13)
        if drivers:
            driver_validation = DataValidation(type="list", formula1=f'"{",".join(drivers)}"')
            driver_validation.error = 'Please select a valid driver from the dropdown'
            driver_validation.errorTitle = 'Invalid Driver'
            ws.add_data_validation(driver_validation)
            driver_validation.add(f'M2:M1000')  # Apply to column M from row 2 to 1000

        # Create dropdown validation for contact persons (column N - 14)
        if contacts:
            contact_validation = DataValidation(type="list", formula1=f'"{",".join(contacts)}"')
            contact_validation.error = 'Please select a valid contact person from the dropdown'
            contact_validation.errorTitle = 'Invalid Contact Person'
            ws.add_data_validation(contact_validation)
            contact_validation.add(f'N2:N1000')  # Apply to column N from row 2 to 1000

        # Create dropdown validation for events (column H - 8)
        if events:
            event_validation = DataValidation(type="list", formula1=f'"{",".join(events)}"')
            event_validation.error = 'Please select a valid event from the dropdown'
            event_validation.errorTitle = 'Invalid Event'
            ws.add_data_validation(event_validation)
            event_validation.add(f'H2:H1000')  # Apply to column H from row 2 to 1000

        # Create dropdown validation for participant types (column I - 9)
        if participant_types:
            type_validation = DataValidation(type="list", formula1=f'"{",".join(participant_types)}"')
            type_validation.error = 'Please select a valid participant type from the dropdown'
            type_validation.errorTitle = 'Invalid Participant Type'
            ws.add_data_validation(type_validation)
            type_validation.add(f'I2:I1000')  # Apply to column I from row 2 to 1000

        # Add sample data row
        sample_data = [
            'John', 'Doe', 'Michael', '<EMAIL>', '+1234567890',
            'University of Example', 'Professor',
            events[0] if events else 'Sample Event',
            participant_types[0] if participant_types else 'Speaker',
            '2024-01-15 10:00', '2024-01-17 18:00',
            hotels[0] if hotels else 'Sample Hotel',
            drivers[0] if drivers else 'John Driver',
            contacts[0] if contacts else 'Jane Contact',
            'Sample participant with assignments'
        ]

        for col, value in enumerate(sample_data, 1):
            ws.cell(row=2, column=col, value=value)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Create response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="participants_template_with_dropdowns.xlsx"'

        # Save workbook to response
        wb.save(response)
        return response

    @action(detail=False, methods=['post'])
    def import_csv(self, request):
        """Import participants from CSV"""
        if 'file' not in request.FILES:
            return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)

        csv_file = request.FILES['file']

        if not csv_file.name.endswith('.csv'):
            return Response({'error': 'File must be a CSV'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Read CSV file
            decoded_file = csv_file.read().decode('utf-8')
            csv_data = csv.DictReader(io.StringIO(decoded_file))

            created_count = 0
            updated_count = 0
            errors = []

            for row_num, row in enumerate(csv_data, start=2):  # Start at 2 because row 1 is header
                try:
                    # Skip comment rows
                    if row.get('First Name', '').startswith('#'):
                        continue

                    # Required fields validation
                    required_fields = ['First Name', 'Last Name', 'Email', 'Event Name', 'Participant Type']
                    missing_fields = [field for field in required_fields if not row.get(field, '').strip()]

                    if missing_fields:
                        errors.append(f"Row {row_num}: Missing required fields: {', '.join(missing_fields)}")
                        continue

                    # Get or validate related objects
                    from events.models import Event
                    from hotels.models import Hotel
                    from drivers.models import Driver
                    from contact_persons.models import ContactPerson

                    try:
                        event = Event.objects.get(name=row['Event Name'].strip())
                    except Event.DoesNotExist:
                        errors.append(f"Row {row_num}: Event '{row['Event Name']}' not found")
                        continue

                    try:
                        participant_type = ParticipantType.objects.get(name=row['Participant Type'].strip())
                    except ParticipantType.DoesNotExist:
                        errors.append(f"Row {row_num}: Participant type '{row['Participant Type']}' not found")
                        continue

                    # Parse dates
                    arrival_date = None
                    departure_date = None

                    if row.get('Arrival Date'):
                        try:
                            arrival_date = datetime.strptime(row['Arrival Date'].strip(), '%Y-%m-%d %H:%M')
                        except ValueError:
                            try:
                                arrival_date = datetime.strptime(row['Arrival Date'].strip(), '%Y-%m-%d')
                            except ValueError:
                                errors.append(f"Row {row_num}: Invalid arrival date format. Use YYYY-MM-DD or YYYY-MM-DD HH:MM")
                                continue

                    if row.get('Departure Date'):
                        try:
                            departure_date = datetime.strptime(row['Departure Date'].strip(), '%Y-%m-%d %H:%M')
                        except ValueError:
                            try:
                                departure_date = datetime.strptime(row['Departure Date'].strip(), '%Y-%m-%d')
                            except ValueError:
                                errors.append(f"Row {row_num}: Invalid departure date format. Use YYYY-MM-DD or YYYY-MM-DD HH:MM")
                                continue

                    # Check if participant exists (by email)
                    email = row['Email'].strip().lower()
                    participant, created = Participant.objects.get_or_create(
                        email=email,
                        defaults={
                            'first_name': row['First Name'].strip(),
                            'last_name': row['Last Name'].strip(),
                            'middle_name': row.get('Middle Name', '').strip(),
                            'phone': row.get('Phone', '').strip(),
                            'institution_name': row.get('Institution Name', '').strip(),
                            'position': row.get('Position', '').strip(),
                            'event': event,
                            'participant_type': participant_type,
                            'arrival_date': arrival_date,
                            'departure_date': departure_date,
                            'remarks': row.get('Remarks', '').strip(),
                            'status': 'pending'
                        }
                    )

                    if created:
                        created_count += 1
                    else:
                        # Update existing participant
                        participant.first_name = row['First Name'].strip()
                        participant.last_name = row['Last Name'].strip()
                        participant.middle_name = row.get('Middle Name', '').strip()
                        participant.phone = row.get('Phone', '').strip()
                        participant.institution_name = row.get('Institution Name', '').strip()
                        participant.position = row.get('Position', '').strip()
                        participant.event = event
                        participant.participant_type = participant_type
                        if arrival_date:
                            participant.arrival_date = arrival_date
                        if departure_date:
                            participant.departure_date = departure_date
                        participant.remarks = row.get('Remarks', '').strip()
                        # Move updated_count increment before save
                        updated_count += 1

                    # Handle assignments
                    if row.get('Assigned Hotel'):
                        try:
                            hotel = Hotel.objects.get(name=row['Assigned Hotel'].strip())
                            participant.assigned_hotel = hotel
                        except Hotel.DoesNotExist:
                            errors.append(f"Row {row_num}: Hotel '{row['Assigned Hotel']}' not found")

                    if row.get('Assigned Driver'):
                        driver_name = row['Assigned Driver'].strip()
                        if ' ' in driver_name:
                            first_name, last_name = driver_name.split(' ', 1)
                            try:
                                driver = Driver.objects.get(first_name=first_name, last_name=last_name)
                                participant.assigned_driver = driver
                            except Driver.DoesNotExist:
                                errors.append(f"Row {row_num}: Driver '{driver_name}' not found")
                        else:
                            errors.append(f"Row {row_num}: Driver name '{driver_name}' should include first and last name")

                    if row.get('Assigned Contact Person'):
                        contact_name = row['Assigned Contact Person'].strip()
                        try:
                            # Try to find by full name
                            contact = ContactPerson.objects.filter(
                                Q(first_name__icontains=contact_name.split()[0]) &
                                Q(last_name__icontains=contact_name.split()[-1])
                            ).first()
                            if contact:
                                participant.assigned_contact_person = contact
                            else:
                                errors.append(f"Row {row_num}: Contact person '{contact_name}' not found")
                        except (IndexError, AttributeError):
                            errors.append(f"Row {row_num}: Invalid contact person name format")

                    participant.save()

                except Exception as e:
                    errors.append(f"Row {row_num}: {str(e)}")
                    continue

            return Response({
                'message': f'Import completed. Created: {created_count}, Updated: {updated_count}',
                'created': created_count,
                'updated': updated_count,
                'errors': errors
            })

        except Exception as e:
            return Response({'error': f'Failed to process CSV: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)


class AttendanceViewSet(viewsets.ModelViewSet):
    queryset = Attendance.objects.all()
    serializer_class = AttendanceSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        participant_id = self.request.query_params.get('participant', None)
        event_schedule_id = self.request.query_params.get('event_schedule', None)

        if participant_id is not None:
            queryset = queryset.filter(participant=participant_id)
        if event_schedule_id is not None:
            queryset = queryset.filter(event_schedule=event_schedule_id)

        return queryset

    @action(detail=False, methods=['post'])
    def check_in(self, request):
        """Check in participant using QR code"""
        uuid = request.data.get('uuid')
        event_schedule_id = request.data.get('event_schedule_id')
        checked_in_by = request.data.get('checked_in_by', '')
        notes = request.data.get('notes', '')

        if not uuid or not event_schedule_id:
            return Response({'error': 'UUID and event_schedule_id required'},
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            participant = Participant.objects.get(uuid=uuid)
            from events.models import EventSchedule
            event_schedule = EventSchedule.objects.get(id=event_schedule_id)

            # Check if already checked in
            if Attendance.objects.filter(participant=participant, event_schedule=event_schedule).exists():
                return Response({'error': 'Already checked in'},
                              status=status.HTTP_400_BAD_REQUEST)

            # Create attendance record
            attendance = Attendance.objects.create(
                participant=participant,
                event_schedule=event_schedule,
                checked_in_by=checked_in_by,
                notes=notes
            )

            serializer = AttendanceSerializer(attendance)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Participant.DoesNotExist:
            return Response({'error': 'Participant not found'},
                          status=status.HTTP_404_NOT_FOUND)
        except EventSchedule.DoesNotExist:
            return Response({'error': 'Event schedule not found'},
                          status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def daily_report(self, request):
        """Generate daily attendance report"""
        date_str = request.query_params.get('date')
        event_id = request.query_params.get('event')

        if not date_str:
            date_str = timezone.now().date().strftime('%Y-%m-%d')

        try:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response({'error': 'Invalid date format. Use YYYY-MM-DD'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Filter attendance records
        queryset = Attendance.objects.filter(checked_in_at__date=report_date)

        if event_id:
            queryset = queryset.filter(event_schedule__event_id=event_id)

        # Get attendance statistics
        total_attendance = queryset.count()
        unique_participants = queryset.values('participant').distinct().count()

        # Group by event schedule
        schedule_stats = queryset.values(
            'event_schedule__title',
            'event_schedule__start_time',
            'event_schedule__event__name'
        ).annotate(
            attendance_count=Count('id')
        ).order_by('event_schedule__start_time')

        # Group by participant type
        type_stats = queryset.values(
            'participant__participant_type__name',
            'participant__participant_type__color'
        ).annotate(
            attendance_count=Count('id')
        ).order_by('participant__participant_type__name')

        return Response({
            'date': date_str,
            'total_attendance': total_attendance,
            'unique_participants': unique_participants,
            'schedule_breakdown': list(schedule_stats),
            'participant_type_breakdown': list(type_stats)
        })

    @action(detail=False, methods=['get'])
    def export_daily_report(self, request):
        """Export daily attendance report as CSV"""
        date_str = request.query_params.get('date')
        event_id = request.query_params.get('event')

        if not date_str:
            date_str = timezone.now().date().strftime('%Y-%m-%d')

        try:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response({'error': 'Invalid date format. Use YYYY-MM-DD'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="attendance_report_{date_str}.csv"'

        writer = csv.writer(response)

        # Write header
        writer.writerow([
            'Participant Name', 'Email', 'Institution', 'Participant Type',
            'Event', 'Session', 'Check-in Time', 'Checked In By', 'Notes'
        ])

        # Filter attendance records
        queryset = Attendance.objects.filter(checked_in_at__date=report_date).select_related(
            'participant', 'participant__participant_type', 'event_schedule', 'event_schedule__event'
        )

        if event_id:
            queryset = queryset.filter(event_schedule__event_id=event_id)

        # Write data rows
        for attendance in queryset:
            writer.writerow([
                attendance.participant.full_name,
                attendance.participant.email,
                attendance.participant.institution_name,
                attendance.participant.participant_type.name,
                attendance.event_schedule.event.name,
                attendance.event_schedule.title,
                attendance.checked_in_at.strftime('%Y-%m-%d %H:%M:%S'),
                attendance.checked_in_by,
                attendance.notes
            ])

        return response


@api_view(['GET'])
@permission_classes([AllowAny])
def check_email_exists(request):
    """Check if an email is already registered for any event"""
    email = request.GET.get('email', '').strip().lower()

    if not email:
        return Response({'error': 'Email parameter is required'}, status=status.HTTP_400_BAD_REQUEST)

    # Check if email exists in any participant record
    exists = Participant.objects.filter(email__iexact=email).exists()

    return Response({
        'exists': exists,
        'email': email
    })


@api_view(['POST', 'GET'])
@permission_classes([AllowAny])
def participant_register(request):
    """Custom participant registration endpoint"""
    if request.method == 'GET':
        # Return registration form information
        from events.models import Event

        events = Event.objects.filter(is_active=True).values('id', 'name', 'description', 'start_date', 'end_date')
        participant_types = ParticipantType.objects.all().values('id', 'name', 'description', 'color')

        return Response({
            'message': 'Participant Registration Endpoint',
            'available_events': list(events),
            'participant_types': list(participant_types),
            'registration_info': {
                'required_fields': ['first_name', 'last_name', 'email', 'phone', 'institution_name', 'position', 'event', 'participant_type'],
                'optional_fields': ['middle_name', 'arrival_date', 'departure_date', 'profile_photo', 'remarks', 'visiting_interests']
            }
        })

    elif request.method == 'POST':
        # Handle registration
        serializer = ParticipantRegistrationSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            participant = serializer.save()

            # Return success response with participant details
            response_serializer = ParticipantSerializer(participant, context={'request': request})
            return Response({
                'message': 'Registration successful! You will receive a confirmation email shortly.',
                'participant': response_serializer.data,
                'status': 'success'
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'message': 'Registration failed. Please check the provided information.',
                'errors': serializer.errors,
                'status': 'error'
            }, status=status.HTTP_400_BAD_REQUEST)
