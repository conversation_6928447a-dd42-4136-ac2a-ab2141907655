from django.db import models
import qrcode
from io import BytesIO
from django.core.files import File
from PIL import Image, ImageDraw, ImageFont
from django.conf import settings
import os
import math
import math


class BadgeTemplate(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    width = models.IntegerField(default=1063)  # 90mm width at 300 DPI
    height = models.IntegerField(default=1654)  # 140mm height at 300 DPI
    background_color = models.CharField(max_length=7, default='#ffffff')
    template_file = models.ImageField(upload_to='badge_templates/', null=True, blank=True)
    is_default = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_default:
            # Ensure only one default template
            BadgeTemplate.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class Badge(models.Model):
    participant = models.OneToOneField('participants.Participant', on_delete=models.CASCADE, related_name='badge')
    template = models.ForeignKey(BadgeTemplate, on_delete=models.CASCADE, null=True, blank=True)

    # QR Code
    qr_code_data = models.TextField()  # JSON data for QR code
    qr_code_image = models.ImageField(upload_to='qr_codes/', null=True, blank=True)

    # Badge File
    badge_image = models.ImageField(upload_to='generated_badges/', null=True, blank=True)

    # Generation Status
    is_generated = models.BooleanField(default=False)
    generated_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Badge for {self.participant.full_name}"

    def generate_qr_code(self):
        """Generate QR code for the participant"""
        import json
        from django.utils import timezone

        qr_data = {
            'participant_id': str(self.participant.uuid),
            'name': self.participant.full_name,
            'event': self.participant.event.name,
            'type': self.participant.participant_type.name,
            'generated_at': timezone.now().isoformat()
        }

        self.qr_code_data = json.dumps(qr_data)

        # Generate QR code image
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        # QR code points to event details page for attendance and information
        event_details_url = f"{settings.QR_CODE_BASE_URL}/event/{self.participant.event.id}/details?participant={self.participant.uuid}"
        qr.add_data(event_details_url)
        qr.make(fit=True)

        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Save QR code image
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        buffer.seek(0)

        filename = f"qr_{self.participant.uuid}.png"
        self.qr_code_image.save(filename, File(buffer), save=False)

        return qr_img

    def _add_subtle_patterns(self, draw, width, height):
        """Add AMAZING subtle patterns to make the badge unique and unforgettable"""
        import math

        # AMAZING Pattern 1: Sophisticated geometric circles with gold accents
        for corner_x, corner_y in [(0, 0), (width, 0), (0, height), (width, height)]:
            for i in range(6):  # Optimized for elegance
                radius = 20 + i * 15
                alpha = max(15, 50 - i * 6)  # Fading effect
                x = corner_x - radius // 2
                y = corner_y - radius // 2

                # Multiple circle layers with gold/blue theme
                gold_intensity = max(0, 255 - i * 30)
                blue_intensity = min(255, 180 + i * 10)
                draw.ellipse([x, y, x + radius, y + radius],
                           outline=(gold_intensity, gold_intensity//2, blue_intensity//3), width=1)

                # Inner glow effect with subtle gold
                if i < 3:
                    inner_radius = radius - 6
                    draw.ellipse([x + 3, y + 3, x + inner_radius + 3, y + inner_radius + 3],
                               outline=(255, 215, 100), width=1)

        # AMAZING Pattern 2: Elegant diagonal lines with gold/blue theme
        line_sets = [
            {'spacing': 80, 'color': (255, 248, 220), 'width': 1},  # Light gold
            {'spacing': 160, 'color': (240, 248, 255), 'width': 1}, # Light blue
            {'spacing': 240, 'color': (255, 235, 180), 'width': 1}  # Subtle gold
        ]

        for line_set in line_sets:
            for i in range(0, width + height, line_set['spacing']):
                x1, y1 = i, 0
                x2, y2 = 0, i
                if x1 <= width and y2 <= height:
                    draw.line([(x1, y1), (x2, y2)], fill=line_set['color'], width=line_set['width'])

                # Reverse diagonal
                x1, y1 = width - i, 0
                x2, y2 = width, i
                if x1 >= 0 and y2 <= height:
                    draw.line([(x1, y1), (x2, y2)], fill=line_set['color'], width=line_set['width'])

        # AMAZING Pattern 3: Sophisticated dot matrix with gold/blue theme
        dot_patterns = [
            {'size': 2, 'spacing': 50, 'color': (255, 245, 200)},  # Gold dots
            {'size': 1, 'spacing': 35, 'color': (240, 248, 255)},  # Blue dots
            {'size': 3, 'spacing': 100, 'color': (255, 235, 180)} # Larger gold dots
        ]

        for pattern in dot_patterns:
            for x in range(pattern['spacing'], width - pattern['spacing'], pattern['spacing']):
                for y in range(180, height - 180, pattern['spacing']):
                    if (x + y) % (pattern['spacing'] * 2) == 0:
                        size = pattern['size']
                        draw.ellipse([x-size, y-size, x+size, y+size], fill=pattern['color'])

        # AMAZING Pattern 4: Elegant wave patterns with gold/blue theme
        wave_configs = [
            {'y_offset': height - 120, 'amplitude': 4, 'frequency': 0.015, 'color': (255, 245, 200)},
            {'y_offset': height - 100, 'amplitude': 6, 'frequency': 0.025, 'color': (240, 248, 255)},
            {'y_offset': height - 80, 'amplitude': 3, 'frequency': 0.035, 'color': (255, 235, 180)}
        ]

        for wave in wave_configs:
            for x in range(0, width, 1):
                wave_offset = int(wave['amplitude'] * math.sin(x * wave['frequency']))
                y_pos = wave['y_offset'] + wave_offset
                draw.point((x, y_pos), fill=wave['color'])
                draw.point((x, y_pos + 1), fill=wave['color'])

        # AMAZING Pattern 5: Golden ratio Fibonacci spiral elements
        fibonacci_centers = [(width//4, height//3), (3*width//4, 2*height//3)]
        for center_x, center_y in fibonacci_centers:
            for i in range(10):
                angle = i * 0.618 * 2 * math.pi  # Golden ratio
                radius = 4 + i * 2.5
                x = center_x + int(radius * math.cos(angle))
                y = center_y + int(radius * math.sin(angle))
                if 0 <= x < width and 0 <= y < height:
                    # Alternate between gold and blue
                    color = (255, 215, 100) if i % 2 == 0 else (200, 220, 255)
                    draw.ellipse([x-1, y-1, x+1, y+1], fill=color)

        # AMAZING Pattern 6: Elegant hexagonal grid with gold accents
        hex_size = 12
        for row in range(0, height // (hex_size * 2) + 1):
            for col in range(0, width // (hex_size * 2) + 1):
                x = col * hex_size * 1.5
                y = row * hex_size * math.sqrt(3)
                if col % 2:
                    y += hex_size * math.sqrt(3) / 2

                if 60 < x < width - 60 and 180 < y < height - 180:
                    # Draw elegant hexagon with alternating colors
                    points = []
                    for i in range(6):
                        angle = i * 60 * math.pi / 180
                        px = x + hex_size * 0.25 * math.cos(angle)
                        py = y + hex_size * 0.25 * math.sin(angle)
                        points.append((px, py))
                    if len(points) == 6:
                        # Alternate between gold and blue hexagons
                        color = (255, 235, 180) if (row + col) % 2 == 0 else (240, 248, 255)
                        draw.polygon(points, outline=color, width=1)

    def _create_event_header(self, draw, width, height):
        """Create a professional header with event branding and ENHANCED VISIBILITY"""
        import math

        # ENHANCED Header with better proportions
        header_height = 140  # Increased for better proportions

        # Get event information
        event = self.participant.event
        event_name = event.name.upper()

        # Create ENHANCED sophisticated gradient - NO BLACK backgrounds, use dark blue/gold
        for y in range(header_height):
            progress = y / header_height
            # Enhanced professional gradient: Dark blue to gold subtle
            r = int(25 + (184 - 25) * progress)  # Dark blue to gold
            g = int(45 + (134 - 45) * progress)  # Dark blue to gold
            b = int(85 + (11 - 85) * progress)   # Dark blue to gold
            draw.line([(0, y), (width, y)], fill=(r, g, b))

        # Add subtle overlay for text readability - NO BLACK, use subtle gold
        overlay_height = 60
        for y in range(20, 20 + overlay_height):
            alpha_progress = abs(y - (20 + overlay_height//2)) / (overlay_height//2)
            alpha = int(20 * (1 - alpha_progress))
            overlay_color = (255, 215, 0, alpha)  # Gold overlay for text contrast
            draw.line([(20, y), (width-20, y)], fill=overlay_color[:3])

        # Add subtle hexagonal pattern overlay
        for x in range(0, width, 30):
            for y in range(0, header_height, 26):
                if (x + y) % 60 == 0:
                    # Draw small hexagon
                    hex_size = 8
                    hex_x, hex_y = x, y
                    points = []
                    for i in range(6):
                        angle = i * 60 * math.pi / 180
                        px = hex_x + hex_size * math.cos(angle)
                        py = hex_y + hex_size * math.sin(angle)
                        points.append((px, py))
                    draw.polygon(points, outline=(255, 255, 255, 20))

        # ENHANCED Event name with superior typography and visibility
        try:
            # Try to load better fonts with larger sizes
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            large_font = ImageFont.load_default()
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            large_font = ImageFont.load_default()

        # ENHANCED text rendering with shadows and outlines for visibility
        def draw_text_with_shadow(text, x, y, font, fill_color='white', shadow_color=(0, 0, 0)):
            # Draw shadow first (offset by 2 pixels)
            draw.text((x + 2, y + 2), text, fill=shadow_color, font=font)
            # Draw outline for better visibility
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    if dx != 0 or dy != 0:
                        draw.text((x + dx, y + dy), text, fill=shadow_color, font=font)
            # Draw main text
            draw.text((x, y), text, fill=fill_color, font=font)

        # Smart text splitting with better line management
        max_chars_per_line = 30 if width > 500 else 25
        if len(event_name) > max_chars_per_line:
            words = event_name.split()
            lines = []
            current_line = ""

            for word in words:
                if len(current_line + word) <= max_chars_per_line:
                    current_line += word + " "
                else:
                    if current_line:
                        lines.append(current_line.strip())
                    current_line = word + " "
            if current_line:
                lines.append(current_line.strip())

            # Draw multiple lines with proper spacing
            line_height = 28
            start_y = 25 if len(lines) <= 2 else 15

            for i, line in enumerate(lines[:3]):  # Max 3 lines
                line_bbox = draw.textbbox((0, 0), line, font=large_font)
                line_width = line_bbox[2] - line_bbox[0]
                x_pos = width//2 - line_width//2
                y_pos = start_y + i * line_height
                draw_text_with_shadow(line, x_pos, y_pos, large_font)
        else:
            # Single line with enhanced visibility
            title_bbox = draw.textbbox((0, 0), event_name, font=large_font)
            title_width = title_bbox[2] - title_bbox[0]
            x_pos = width//2 - title_width//2
            y_pos = 35
            draw_text_with_shadow(event_name, x_pos, y_pos, large_font)

        # Event dates
        if hasattr(event, 'start_date') and event.start_date:
            if hasattr(event, 'end_date') and event.end_date and event.start_date.date() != event.end_date.date():
                date_text = f"{event.start_date.strftime('%B %d')} - {event.end_date.strftime('%d, %Y')}"
            else:
                date_text = event.start_date.strftime('%B %d, %Y')

            date_bbox = draw.textbbox((0, 0), date_text, font=subtitle_font)
            date_width = date_bbox[2] - date_bbox[0]
            draw.text((width//2 - date_width//2, 70), date_text, fill='#E8F4FD', font=subtitle_font)

        # Location
        if hasattr(event, 'location') and event.location:
            location_text = event.location
            if len(location_text) > 35:
                location_text = location_text[:32] + "..."

            location_bbox = draw.textbbox((0, 0), location_text, font=subtitle_font)
            location_width = location_bbox[2] - location_bbox[0]
            draw.text((width//2 - location_width//2, 90), location_text, fill='#E8F4FD', font=subtitle_font)

        # Decorative elements
        center_x = width // 2
        draw.ellipse([center_x-3, 110, center_x+3, 116], fill='white')
        draw.line([(50, 110), (center_x-10, 110)], fill='white', width=2)
        draw.line([(center_x+10, 110), (width-50, 110)], fill='white', width=2)

    def _add_participant_photo(self, draw, width, height):
        """Add participant photo or fallback to organizer photo with ENHANCED QUALITY"""
        photo_size = 150  # Increased size for better quality
        photo_x = width // 2 - photo_size // 2
        photo_y = 100  # Positioned to intersect with banner as requested

        # Try to get participant photo first
        participant_photo = None
        if self.participant.profile_photo:
            try:
                participant_photo = Image.open(self.participant.profile_photo.path)
            except:
                participant_photo = None

        # If no participant photo, try to get primary organizer photo
        if not participant_photo:
            try:
                from events.models import EventOrganizer
                primary_organizer = EventOrganizer.objects.filter(
                    event=self.participant.event,
                    is_primary=True
                ).first()

                if primary_organizer and primary_organizer.photo:
                    participant_photo = Image.open(primary_organizer.photo.path)
                else:
                    # Get any organizer photo
                    any_organizer = EventOrganizer.objects.filter(
                        event=self.participant.event
                    ).first()
                    if any_organizer and any_organizer.photo:
                        participant_photo = Image.open(any_organizer.photo.path)
            except:
                participant_photo = None

        if participant_photo:
            # Resize and make circular
            participant_photo = participant_photo.resize((photo_size, photo_size))

            # Create circular mask
            mask = Image.new('L', (photo_size, photo_size), 0)
            mask_draw = ImageDraw.Draw(mask)
            mask_draw.ellipse([0, 0, photo_size, photo_size], fill=255)

            # Apply mask to photo
            participant_photo.putalpha(mask)

            # Create elegant photo frame
            frame_padding = 8
            frame_size = photo_size + frame_padding * 2
            frame_x = photo_x - frame_padding
            frame_y = photo_y - frame_padding

            # Outer frame with gradient
            for i in range(frame_padding):
                alpha = int(255 * (1 - i / frame_padding))
                color = (25, 45, 85, alpha)
                draw.ellipse([
                    frame_x + i, frame_y + i,
                    frame_x + frame_size - i, frame_y + frame_size - i
                ], outline=color[:3], width=1)

            # Inner white frame
            draw.ellipse([
                frame_x + frame_padding - 2, frame_y + frame_padding - 2,
                frame_x + frame_size - frame_padding + 2, frame_y + frame_size - frame_padding + 2
            ], outline='white', width=3)

            # Paste the photo
            badge_img = draw._image
            badge_img.paste(participant_photo, (photo_x, photo_y), participant_photo)
        else:
            # Create placeholder with university icon
            placeholder_color = (240, 248, 255)
            draw.ellipse([photo_x, photo_y, photo_x + photo_size, photo_y + photo_size],
                        fill=placeholder_color, outline=(25, 45, 85), width=3)

            # Add university icon
            icon_text = "🎓"
            try:
                icon_font = ImageFont.load_default()
            except:
                icon_font = ImageFont.load_default()

            icon_bbox = draw.textbbox((0, 0), icon_text, font=icon_font)
            icon_width = icon_bbox[2] - icon_bbox[0]
            icon_height = icon_bbox[3] - icon_bbox[1]
            draw.text((photo_x + photo_size//2 - icon_width//2, photo_y + photo_size//2 - icon_height//2),
                     icon_text, fill=(25, 45, 85), font=icon_font)

    def _add_sponsors_section(self, draw, width, height):
        """Add MULTIPLE sponsors and organizers logos with ENHANCED QUALITY"""
        try:
            from events.models import EventSponsor, EventOrganizer

            # Get MORE sponsors and organizers for better showcase
            sponsors = EventSponsor.objects.filter(
                event=self.participant.event,
                is_active=True
            ).order_by('display_order')[:6]  # Increased to 6 sponsors

            organizers = EventOrganizer.objects.filter(
                event=self.participant.event,
                is_active=True
            ).order_by('display_order')[:4]  # Increased to 4 organizers

            if sponsors.exists() or organizers.exists():
                # ENHANCED sponsors section - HORIZONTAL layout at bottom as requested
                sponsors_y = height - 100  # Bottom positioning
                sponsors_height = 80  # Optimized height for horizontal layout

                # Enhanced gradient background for sponsors section
                for y in range(sponsors_height):
                    progress = y / sponsors_height
                    # Elegant gradient from light to lighter
                    r = int(248 + (255 - 248) * progress)
                    g = int(250 + (255 - 250) * progress)
                    b = int(252 + (255 - 252) * progress)
                    draw.line([(5, sponsors_y + y), (width-5, sponsors_y + y)], fill=(r, g, b))

                # Enhanced separator with decorative elements
                separator_y = sponsors_y + 5
                draw.line([(40, separator_y), (width-40, separator_y)], fill=(200, 220, 240), width=2)

                # Add decorative corner elements
                for x in [40, width-40]:
                    draw.ellipse([x-3, separator_y-3, x+3, separator_y+3], fill=(180, 200, 230))

                # ENHANCED multi-row layout for better organization
                total_logos = len(sponsors) + len(organizers)
                if total_logos > 0:
                    # Calculate optimal layout
                    max_per_row = 4 if total_logos > 4 else total_logos
                    rows_needed = (total_logos + max_per_row - 1) // max_per_row

                    logo_size = min(50, (width - 80) // max_per_row - 15)  # Larger logos
                    row_height = logo_size + 15

                    current_logo = 0

                    # ENHANCED sponsor logos with multi-row layout
                    for sponsor in sponsors:
                        if sponsor.logo:
                            try:
                                # Calculate position in grid
                                row = current_logo // max_per_row
                                col = current_logo % max_per_row

                                # Calculate positions
                                logos_in_row = min(max_per_row, total_logos - row * max_per_row)
                                row_width = logos_in_row * logo_size + (logos_in_row - 1) * 15
                                start_x = (width - row_width) // 2

                                x_pos = start_x + col * (logo_size + 15)
                                y_pos = sponsors_y + 20 + row * row_height

                                # Load and enhance sponsor logo
                                sponsor_img = Image.open(sponsor.logo.path)
                                sponsor_img = sponsor_img.resize((logo_size, logo_size), Image.Resampling.LANCZOS)

                                # Create enhanced background with gradient border
                                bg_size = logo_size + 8
                                logo_bg = Image.new('RGB', (bg_size, bg_size), 'white')
                                logo_bg_draw = ImageDraw.Draw(logo_bg)

                                # Multi-layer border for depth
                                logo_bg_draw.rectangle([0, 0, bg_size-1, bg_size-1],
                                                     outline=(200, 220, 240), width=2)
                                logo_bg_draw.rectangle([2, 2, bg_size-3, bg_size-3],
                                                     outline=(220, 235, 255), width=1)

                                # Add subtle shadow
                                shadow_bg = Image.new('RGBA', (bg_size + 4, bg_size + 4), (0, 0, 0, 0))
                                shadow_draw = ImageDraw.Draw(shadow_bg)
                                shadow_draw.rectangle([2, 2, bg_size+1, bg_size+1],
                                                    fill=(0, 0, 0, 30))

                                # Paste sponsor logo with proper centering
                                logo_bg.paste(sponsor_img, (4, 4))

                                # Paste to badge with shadow
                                badge_img = draw._image
                                if shadow_bg.mode == 'RGBA':
                                    badge_img.paste(shadow_bg, (x_pos - 2, y_pos - 2), shadow_bg)
                                badge_img.paste(logo_bg, (x_pos, y_pos))

                                # Add sponsor type badge
                                type_text = sponsor.sponsor_type.upper()[:4]
                                type_font = ImageFont.load_default()
                                type_bbox = draw.textbbox((0, 0), type_text, font=type_font)
                                type_width = type_bbox[2] - type_bbox[0]

                                # Type badge colors
                                type_colors = {
                                    'PLAT': (255, 215, 0),  # Gold
                                    'GOLD': (255, 165, 0),  # Orange
                                    'SILV': (192, 192, 192),  # Silver
                                    'BRON': (205, 127, 50),  # Bronze
                                }
                                type_color = type_colors.get(type_text, (100, 149, 237))

                                badge_x = x_pos + logo_size - type_width - 8
                                badge_y = y_pos - 8
                                draw.rectangle([badge_x - 2, badge_y - 2, badge_x + type_width + 2, badge_y + 12],
                                             fill=type_color)
                                draw.text((badge_x, badge_y), type_text, fill='white', font=type_font)

                                current_logo += 1

                            except Exception as e:
                                print(f"Error loading sponsor logo: {e}")
                                current_logo += 1

                    # ENHANCED organizer photos with better quality and positioning
                    for organizer in organizers:
                        if organizer.photo:
                            try:
                                # Calculate position in grid (continuing from sponsors)
                                row = current_logo // max_per_row
                                col = current_logo % max_per_row

                                # Calculate positions
                                logos_in_row = min(max_per_row, total_logos - row * max_per_row)
                                row_width = logos_in_row * logo_size + (logos_in_row - 1) * 15
                                start_x = (width - row_width) // 2

                                x_pos = start_x + col * (logo_size + 15)
                                y_pos = sponsors_y + 20 + row * row_height

                                # Load and enhance organizer photo
                                org_img = Image.open(organizer.photo.path)
                                org_img = org_img.resize((logo_size, logo_size), Image.Resampling.LANCZOS)

                                # Create enhanced circular mask
                                mask = Image.new('L', (logo_size, logo_size), 0)
                                mask_draw = ImageDraw.Draw(mask)
                                mask_draw.ellipse([0, 0, logo_size, logo_size], fill=255)

                                # Apply mask for circular photo
                                org_img.putalpha(mask)

                                # Create enhanced circular frame with gradient
                                frame_size = logo_size + 8
                                for i in range(4):
                                    frame_color = (25 + i * 10, 45 + i * 15, 85 + i * 20)
                                    draw.ellipse([
                                        x_pos - 4 + i, y_pos - 4 + i,
                                        x_pos + logo_size + 4 - i, y_pos + logo_size + 4 - i
                                    ], outline=frame_color, width=1)

                                # Add organizer role badge
                                role_text = "ORG" if not organizer.is_primary else "LEAD"
                                role_font = ImageFont.load_default()
                                role_bbox = draw.textbbox((0, 0), role_text, font=role_font)
                                role_width = role_bbox[2] - role_bbox[0]

                                role_color = (220, 38, 38) if organizer.is_primary else (34, 197, 94)
                                badge_x = x_pos + logo_size - role_width - 8
                                badge_y = y_pos - 8

                                draw.rectangle([badge_x - 2, badge_y - 2, badge_x + role_width + 2, badge_y + 12],
                                             fill=role_color)
                                draw.text((badge_x, badge_y), role_text, fill='white', font=role_font)

                                # Paste organizer photo
                                badge_img = draw._image
                                badge_img.paste(org_img, (x_pos, y_pos), org_img)

                                current_logo += 1

                            except Exception as e:
                                print(f"Error loading organizer photo: {e}")
                                current_logo += 1

                # Add labels
                try:
                    label_font = ImageFont.load_default()
                except:
                    label_font = ImageFont.load_default()

                if sponsors.exists():
                    sponsor_label = "Sponsors"
                    if organizers.exists():
                        sponsor_label += " & Organizers"
                else:
                    sponsor_label = "Organizers"

                label_bbox = draw.textbbox((0, 0), sponsor_label, font=label_font)
                label_width = label_bbox[2] - label_bbox[0]
                draw.text((width//2 - label_width//2, sponsors_y + 55),
                         sponsor_label, fill=(100, 116, 139), font=label_font)

        except Exception as e:
            print(f"Error adding sponsors section: {e}")

    def _add_participant_info_new(self, draw, width, height):
        """Add participant information with ENHANCED professional styling and visibility
        Order: Full name, Institution, Position, then participant type (bold and colorful)"""
        # ENHANCED Participant name with better positioning
        name = self.participant.full_name.upper()
        name_y = 280  # Positioned after intersecting photo

        # Create ENHANCED elegant name background with gradient
        name_bg_height = 55  # Increased height
        name_bg_y = name_y - 10

        # ENHANCED gradient background for name with better visibility
        for y in range(name_bg_height):
            progress = y / name_bg_height
            # Enhanced gradient with better contrast
            r = int(235 + (250 - 235) * progress)
            g = int(245 + (255 - 245) * progress)
            b = int(250 + (255 - 250) * progress)
            draw.line([(25, name_bg_y + y), (width-25, name_bg_y + y)], fill=(r, g, b))

        # Add elegant border to name section
        draw.rectangle([25, name_bg_y, width-25, name_bg_y + name_bg_height],
                      outline=(200, 220, 240), width=2)

        # ENHANCED Name text with superior shadow and outline
        try:
            name_font = ImageFont.load_default()
            large_name_font = ImageFont.load_default()
        except:
            name_font = ImageFont.load_default()
            large_name_font = ImageFont.load_default()

        # Enhanced text rendering function
        def draw_enhanced_text(text, x, y, font, main_color=(25, 45, 85)):
            # Multiple shadow layers for depth
            shadow_offsets = [(3, 3), (2, 2), (1, 1)]
            shadow_colors = [(180, 180, 180), (200, 200, 200), (220, 220, 220)]

            for offset, shadow_color in zip(shadow_offsets, shadow_colors):
                draw.text((x + offset[0], y + offset[1]), text, fill=shadow_color, font=font)

            # Outline for better visibility
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    if dx != 0 or dy != 0:
                        draw.text((x + dx, y + dy), text, fill=(100, 100, 100), font=font)

            # Main text with enhanced color
            draw.text((x, y), text, fill=main_color, font=font)

        # Smart name fitting
        name_bbox = draw.textbbox((0, 0), name, font=large_name_font)
        name_width = name_bbox[2] - name_bbox[0]

        # If name is too long, try smaller font or split
        if name_width > width - 60:
            name_bbox = draw.textbbox((0, 0), name, font=name_font)
            name_width = name_bbox[2] - name_bbox[0]
            font_to_use = name_font
        else:
            font_to_use = large_name_font

        x_pos = width//2 - name_width//2
        draw_enhanced_text(name, x_pos, name_y, font_to_use)

        # Institution name (after name as requested)
        institution = self.participant.institution_name or 'Unknown Institution'
        if len(institution) > 40:
            institution = institution[:37] + "..."

        inst_y = name_y + 40
        try:
            inst_font = ImageFont.load_default()
        except:
            inst_font = ImageFont.load_default()

        inst_bbox = draw.textbbox((0, 0), institution, font=inst_font)
        inst_width = inst_bbox[2] - inst_bbox[0]
        draw_enhanced_text(institution, width//2 - inst_width//2, inst_y, inst_font, (60, 80, 120))

        # Position (after institution as requested)
        position_y = inst_y + 30
        if hasattr(self.participant, 'position') and self.participant.position:
            position = self.participant.position
            if len(position) > 35:
                position = position[:32] + "..."

            pos_bbox = draw.textbbox((0, 0), position, font=inst_font)
            pos_width = pos_bbox[2] - pos_bbox[0]
            draw_enhanced_text(position, width//2 - pos_width//2, position_y, inst_font, (80, 100, 140))
        else:
            # Show placeholder for position
            position = "Position Not Specified"
            pos_bbox = draw.textbbox((0, 0), position, font=inst_font)
            pos_width = pos_bbox[2] - pos_bbox[0]
            draw_enhanced_text(position, width//2 - pos_width//2, position_y, inst_font, (120, 120, 120))

        # Add line break before participant type as requested
        line_break_y = position_y + 40
        draw.line([(50, line_break_y), (width-50, line_break_y)], fill=(200, 220, 240), width=2)

        # Participant type badge (BOLD AND COLORFUL as requested)
        ptype = self.participant.participant_type.name.upper()
        ptype_y = line_break_y + 20

        # Create professional type badge - MORE BOLD AND COLORFUL
        badge_width = min(200, len(ptype) * 14 + 50)  # Larger badge
        badge_height = 40  # Taller badge
        badge_x = width//2 - badge_width//2

        # Determine badge color - MORE VIBRANT
        if 'VIP' in ptype or 'SPEAKER' in ptype:
            badge_color = (220, 38, 38)
            accent_color = (255, 68, 68)
        elif 'STAFF' in ptype or 'ORGANIZER' in ptype:
            badge_color = (5, 150, 105)
            accent_color = (34, 255, 94)
        elif 'STUDENT' in ptype:
            badge_color = (245, 158, 11)
            accent_color = (255, 215, 0)
        else:
            badge_color = (25, 45, 85)
            accent_color = (59, 130, 255)

        # Create gradient badge with enhanced colors
        for y in range(badge_height):
            progress = y / badge_height
            r = int(badge_color[0] + (accent_color[0] - badge_color[0]) * progress)
            g = int(badge_color[1] + (accent_color[1] - badge_color[1]) * progress)
            b = int(badge_color[2] + (accent_color[2] - badge_color[2]) * progress)
            draw.line([(badge_x, ptype_y + y), (badge_x + badge_width, ptype_y + y)], fill=(r, g, b))

        # Badge border and text - ENHANCED
        draw.rectangle([badge_x, ptype_y, badge_x + badge_width, ptype_y + badge_height],
                      outline='white', width=3)

        try:
            badge_font = ImageFont.load_default()
        except:
            badge_font = ImageFont.load_default()

        ptype_bbox = draw.textbbox((0, 0), ptype, font=badge_font)
        ptype_width = ptype_bbox[2] - ptype_bbox[0]

        # Add shadow for bold effect
        draw.text((width//2 - ptype_width//2 + 2, ptype_y + 12), ptype, fill=(0, 0, 0, 100), font=badge_font)
        draw.text((width//2 - ptype_width//2, ptype_y + 10), ptype, fill='white', font=badge_font)

    def _add_qr_code_new(self, draw, width, height, qr_img):
        """Add QR code with professional frame - positioned after participant type"""
        qr_size = 100
        qr_y = height - 250  # More space for logos below
        qr_img_resized = qr_img.resize((qr_size, qr_size))

        # Create elegant QR frame
        frame_padding = 12
        frame_x1 = width//2 - qr_size//2 - frame_padding
        frame_y1 = qr_y - frame_padding
        frame_x2 = width//2 + qr_size//2 + frame_padding
        frame_y2 = qr_y + qr_size + frame_padding

        # Multi-layer frame
        draw.rectangle([frame_x1-2, frame_y1-2, frame_x2+2, frame_y2+2],
                      fill=(25, 45, 85), outline=(70, 120, 180), width=1)
        draw.rectangle([frame_x1, frame_y1, frame_x2, frame_y2],
                      fill='white', outline=(25, 45, 85), width=1)

        # Corner decorations
        corner_size = 6
        corners = [
            (frame_x1+3, frame_y1+3), (frame_x2-3, frame_y1+3),
            (frame_x1+3, frame_y2-3), (frame_x2-3, frame_y2-3)
        ]

        for corner_x, corner_y in corners:
            draw.line([(corner_x-corner_size, corner_y), (corner_x+corner_size, corner_y)],
                     fill=(25, 45, 85), width=2)
            draw.line([(corner_x, corner_y-corner_size), (corner_x, corner_y+corner_size)],
                     fill=(25, 45, 85), width=2)

        # Paste QR code
        badge_img = draw._image
        badge_img.paste(qr_img_resized, (width//2 - qr_size//2, qr_y))

        # QR label
        qr_label = "SCAN TO VERIFY"
        qr_label_y = qr_y + qr_size + 20

        try:
            qr_font = ImageFont.load_default()
        except:
            qr_font = ImageFont.load_default()

        label_bbox = draw.textbbox((0, 0), qr_label, font=qr_font)
        label_width = label_bbox[2] - label_bbox[0]

        # Label background
        label_bg_x1 = width//2 - label_width//2 - 6
        label_bg_y1 = qr_label_y - 2
        label_bg_x2 = width//2 + label_width//2 + 6
        label_bg_y2 = qr_label_y + 12

        draw.rectangle([label_bg_x1, label_bg_y1, label_bg_x2, label_bg_y2],
                      fill=(240, 248, 255), outline=(25, 45, 85), width=1)
        draw.text((width//2 - label_width//2, qr_label_y), qr_label, fill=(25, 45, 85), font=qr_font)

    def _create_professional_header(self, draw, width, height):
        """Create a professional header with University branding"""
        # Header background with elegant gradient
        header_height = 120

        # Create sophisticated gradient - University colors
        for y in range(header_height):
            # Elegant blue to dark blue gradient
            progress = y / header_height
            r = int(25 + (45 - 25) * progress)  # 25 to 45
            g = int(45 + (85 - 45) * progress)  # 45 to 85
            b = int(85 + (135 - 85) * progress)  # 85 to 135
            draw.line([(0, y), (width, y)], fill=(r, g, b))

        # Add subtle pattern overlay
        for x in range(0, width, 40):
            for y in range(0, header_height, 40):
                if (x + y) % 80 == 0:
                    draw.ellipse([x-2, y-2, x+2, y+2], fill=(255, 255, 255, 30))

        # University name - use default fonts for better compatibility
        try:
            # Try to load system fonts, fallback to default
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()

        # Main title
        title_text = "UNIVERSITY OF GONDAR"
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        draw.text((width//2 - title_width//2, 25), title_text, fill='white', font=title_font)

        # Subtitle
        subtitle_text = "EVENT MANAGEMENT SYSTEM"
        subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        draw.text((width//2 - subtitle_width//2, 55), subtitle_text, fill='#E8F4FD', font=subtitle_font)

        # Decorative line
        line_y = 85
        draw.line([(50, line_y), (width-50, line_y)], fill='white', width=2)

        # Add small decorative elements
        center_x = width // 2
        draw.ellipse([center_x-3, line_y-3, center_x+3, line_y+3], fill='white')

    def _add_elegant_border(self, draw, width, height):
        """Add an elegant border to the badge"""
        # Outer border - dark
        border_color = (25, 45, 85)
        draw.rectangle([0, 0, width-1, height-1], outline=border_color, width=3)

        # Inner border - lighter
        inner_border_color = (70, 120, 180)
        draw.rectangle([5, 5, width-6, height-6], outline=inner_border_color, width=1)

        # Corner decorations
        corner_size = 15
        corner_color = (25, 45, 85)

        # Top-left corner
        draw.line([(10, 10), (10+corner_size, 10)], fill=corner_color, width=2)
        draw.line([(10, 10), (10, 10+corner_size)], fill=corner_color, width=2)

        # Top-right corner
        draw.line([(width-10-corner_size, 10), (width-10, 10)], fill=corner_color, width=2)
        draw.line([(width-10, 10), (width-10, 10+corner_size)], fill=corner_color, width=2)

        # Bottom-left corner
        draw.line([(10, height-10), (10+corner_size, height-10)], fill=corner_color, width=2)
        draw.line([(10, height-10-corner_size), (10, height-10)], fill=corner_color, width=2)

        # Bottom-right corner
        draw.line([(width-10-corner_size, height-10), (width-10, height-10)], fill=corner_color, width=2)
        draw.line([(width-10, height-10-corner_size), (width-10, height-10)], fill=corner_color, width=2)

    def _add_logo_area(self, draw, width):
        """Add a placeholder for university logo"""
        logo_size = 60
        logo_x = width // 2 - logo_size // 2
        logo_y = 140

        # Logo background circle
        draw.ellipse([logo_x-5, logo_y-5, logo_x+logo_size+5, logo_y+logo_size+5],
                    fill=(25, 45, 85), outline=(70, 120, 180), width=2)
        draw.ellipse([logo_x, logo_y, logo_x+logo_size, logo_y+logo_size],
                    fill='white', outline=(25, 45, 85), width=1)

        # University icon (simplified)
        try:
            icon_font = ImageFont.load_default()
        except:
            icon_font = ImageFont.load_default()

        icon_text = "🎓"
        icon_bbox = draw.textbbox((0, 0), icon_text, font=icon_font)
        icon_width = icon_bbox[2] - icon_bbox[0]
        icon_height = icon_bbox[3] - icon_bbox[1]
        draw.text((logo_x + logo_size//2 - icon_width//2, logo_y + logo_size//2 - icon_height//2),
                 icon_text, fill=(25, 45, 85), font=icon_font)

    def generate_badge(self):
        """Generate badge using exact SVG template design"""
        from django.utils import timezone
        import os

        # Get or create QR code
        if not self.qr_code_image:
            qr_img = self.generate_qr_code()
        else:
            qr_img = Image.open(self.qr_code_image.path)

        # SVG template dimensions (600x1200)
        width = 600
        height = 1200

        # Create badge with gradient background
        badge_img = Image.new('RGB', (width, height), color='#002D72')
        draw = ImageDraw.Draw(badge_img)

        # Add gradient background
        self._add_svg_gradient_background(draw, width, height)

        # Add decorative elements
        self._add_svg_decorative_elements(draw, width, height)

        # Add top decorative ribbon
        self._add_svg_top_ribbon(draw, width, height)

        # Add ministry logo
        self._add_svg_ministry_logo(draw, width, height)

        # Add ministry title
        self._add_svg_ministry_title(draw, width, height)

        # Add theme
        self._add_svg_theme(draw, width, height)

        # Add photo area
        self._add_svg_photo_area(draw, width, height)

        # Add participant info
        self._add_svg_participant_info(draw, width, height)

        # Add participant type badge
        self._add_svg_participant_type(draw, width, height)

        # Add QR code frame
        self._add_svg_qr_code(draw, width, height, qr_img)

        # Use EXACT SVG design instead
        return self._generate_exact_svg_badge(qr_img)

        # Save the badge image
        buffer = BytesIO()
        badge_img.save(buffer, format='PNG', quality=95, optimize=True)
        buffer.seek(0)

        # Generate filename
        filename = f"badge_{self.participant.id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.png"

        # Save to model
        self.badge_image.save(filename, File(buffer), save=False)
        self.is_generated = True
        self.generated_at = timezone.now()
        self.save()

        return badge_img

    def _generate_exact_svg_badge(self, qr_img):
        """Generate badge using EXACT SVG template design with precise fonts and positioning"""
        from django.utils import timezone
        import os

        # HIGH RESOLUTION dimensions for better quality
        width = 1200  # Double resolution
        height = 2400  # Double resolution

        # Create badge with exact gradient background
        badge_img = Image.new('RGB', (width, height), color='#002D72')
        draw = ImageDraw.Draw(badge_img)

        # Add exact gradient background (#002D72 to #021B3A)
        for y in range(height):
            progress = y / height
            r = int(0 + (2 - 0) * progress)
            g = int(45 + (27 - 45) * progress)
            b = int(114 + (58 - 114) * progress)
            color = (r, g, b)
            draw.line([(0, y), (width, y)], fill=color)

        # Add subtle gold pattern overlay
        self._add_exact_gold_pattern(draw, width, height)

        # Professional header section (0-180px)
        draw.rectangle([0, 0, width, 180], fill=(0, 75, 145, 217))  # #004B91 with opacity
        draw.rectangle([0, 180, width, 210], fill=(0, 75, 145, 153))  # #004B91 with opacity

        # ENHANCED fonts for better visibility - keeping original positions
        try:
            # Ministry title fonts - LARGER but not doubled
            ministry_font = ImageFont.truetype("arial.ttf", 32)  # Increased from 28
            sector_font = ImageFont.truetype("arial.ttf", 26)    # Increased from 22
            theme_font = ImageFont.truetype("arial.ttf", 24)     # Increased from 20

            # Participant info fonts - MUCH LARGER for visibility
            name_font = ImageFont.truetype("arial.ttf", 42)      # Much larger for visibility
            position_font = ImageFont.truetype("arial.ttf", 32)  # Much larger for visibility
            institution_font = ImageFont.truetype("arial.ttf", 28) # Much larger for visibility

            # Participant type font - LARGER for prominence
            type_font = ImageFont.truetype("arial.ttf", 30)      # Larger for prominence

            # QR and other fonts - LARGER for visibility
            qr_font = ImageFont.truetype("arial.ttf", 22)        # Larger for visibility
            sponsor_font = ImageFont.truetype("arial.ttf", 18)   # Larger for visibility
            footer_font = ImageFont.truetype("arial.ttf", 20)    # Larger for visibility
            small_font = ImageFont.truetype("arial.ttf", 18)     # Larger for visibility
        except:
            # Fallback fonts
            ministry_font = ImageFont.load_default()
            sector_font = ImageFont.load_default()
            theme_font = ImageFont.load_default()
            name_font = ImageFont.load_default()
            position_font = ImageFont.load_default()
            institution_font = ImageFont.load_default()
            type_font = ImageFont.load_default()
            qr_font = ImageFont.load_default()
            sponsor_font = ImageFont.load_default()
            footer_font = ImageFont.load_default()
            small_font = ImageFont.load_default()

        # HIGH QUALITY header styling scaled for double resolution
        # Ministry title at scaled position with enhanced visibility
        self._draw_text_with_glow((600, 140), "MINISTRY OF EDUCATION OF FDRE",
                                 fill='white', font=ministry_font, draw=draw,
                                 letter_spacing=1, weight='bold')

        # HIGH QUALITY gold line scaled for double resolution
        draw.line([(360, 160), (840, 160)], fill='#FFD700', width=4)
        # Add rounded caps effect
        draw.ellipse([356, 158, 364, 162], fill='#FFD700')
        draw.ellipse([836, 158, 844, 162], fill='#FFD700')

        # Sector title at original position with enhanced visibility
        self._draw_text_with_glow((300, 120), "HIGHER EDUCATION DEVELOPMENT SECTOR",
                                 fill='white', font=sector_font, draw=draw, weight='semibold')

        # Theme at original position with enhanced visibility
        self._draw_text_with_glow((300, 160), "HIGHER EDUCATION FOR HIGHER IMPACT",
                                 fill='#FFD700', font=theme_font, draw=draw,
                                 letter_spacing=1.5, weight='semibold')

        # Photo area with original positioning and enhanced visibility
        draw.rounded_rectangle([150, 240, 450, 540], radius=20, fill=None,
                             outline='#FFD700', width=3)
        draw.rectangle([170, 260, 430, 520], fill=(255, 255, 255, 8),
                      outline=(255, 255, 255, 64), width=1)

        # Try to load participant photo with HIGH QUALITY processing
        photo_loaded = False
        if hasattr(self.participant, 'profile_photo') and self.participant.profile_photo:
            try:
                participant_photo = Image.open(self.participant.profile_photo.path)

                # Convert to RGB if needed for better quality
                if participant_photo.mode != 'RGB':
                    participant_photo = participant_photo.convert('RGB')

                # HIGH QUALITY resize with anti-aliasing - original size
                photo_size = (260, 260)  # Original frame size with high quality
                participant_photo = participant_photo.resize(photo_size, Image.Resampling.LANCZOS)

                # Apply subtle enhancement
                from PIL import ImageEnhance
                enhancer = ImageEnhance.Sharpness(participant_photo)
                participant_photo = enhancer.enhance(1.2)  # Slight sharpening

                enhancer = ImageEnhance.Contrast(participant_photo)
                participant_photo = enhancer.enhance(1.1)  # Slight contrast boost

                badge_img.paste(participant_photo, (170, 260))  # Original position
                photo_loaded = True
                print(f"✅ HIGH QUALITY photo loaded and enhanced")
            except Exception as e:
                print(f"❌ Error loading photo: {e}")

        # If no photo, show theme text at original position
        if not photo_loaded:
            draw.text((300, 400), "PARTICIPANT PHOTO", fill=(255, 255, 255, 144),
                     font=qr_font, anchor='mm')

        # Participant info with original positioning and enhanced visibility
        # Full name positioned below photo area with larger font
        full_name = self.participant.full_name
        draw.text((300, 570), full_name, fill='white', font=name_font, anchor='mm')

        # HIGH QUALITY gold dashed line with glow effect - original positioning
        # Glow effect
        for i in range(3):
            y_pos = 580 + i - 1
            alpha = 100 - (i * 30)
            for x in range(200, 400, 8):  # Original spacing
                draw.line([(x, y_pos), (x+5, y_pos)], fill=(255, 215, 0, alpha), width=2)
        # Main dashed line
        for x in range(200, 400, 8):
            draw.line([(x, 580), (x+5, 580)], fill='#FFD700', width=2)

        # Position with enhanced visibility - original positioning
        position = getattr(self.participant, 'position', 'Position Title')
        draw.text((300, 600), position, fill=(255, 255, 255, 221), font=position_font, anchor='mm')

        # Institution with enhanced visibility - original positioning
        institution = getattr(self.participant, 'institution_name', 'Institution Name')
        draw.text((300, 630), institution, fill=(255, 255, 255, 170), font=institution_font, anchor='mm')

        # Participant type with ENHANCED prominence and styling - original positioning
        # Background with gradient effect
        draw.rounded_rectangle([140, 640, 460, 710], radius=30, fill=(255, 215, 0, 30),
                             outline='#FFD700', width=4)
        draw.rounded_rectangle([150, 650, 450, 700], radius=25, fill=(255, 215, 0, 50),
                             outline='#FFEC8B', width=2)

        # Enhanced gold waves with multiple layers - original positioning
        for i in range(3):
            alpha = 255 - (i * 60)
            draw.line([(160, 675+i), (180, 665+i), (200, 675+i)], fill=(255, 215, 0, alpha), width=3)
            draw.line([(420, 675+i), (440, 665+i), (460, 675+i)], fill=(255, 215, 0, alpha), width=3)

        # Type text with shadow effect for prominence - original positioning
        ptype = getattr(self.participant.participant_type, 'name', 'PARTICIPANT TYPE') if self.participant.participant_type else 'PARTICIPANT TYPE'
        # Text shadow
        draw.text((302, 677), ptype.upper(), fill=(0, 0, 0, 100), font=type_font, anchor='mm')
        # Main text
        draw.text((300, 675), ptype.upper(), fill='#FFD700', font=type_font, anchor='mm')

        # QR code with HIGH QUALITY positioning - original positioning
        draw.rounded_rectangle([175, 720, 425, 970], radius=15, fill=(255, 255, 255, 16),
                             outline='#FFD700', width=2)
        draw.rectangle([200, 745, 400, 945], fill='white')

        # Paste QR code at original position with HIGH QUALITY
        qr_resized = qr_img.resize((200, 200), Image.Resampling.LANCZOS)
        badge_img.paste(qr_resized, (200, 745))

        # NO TEXT BELOW QR CODE - as requested

        # Sponsors section with HIGH QUALITY styling - original positioning
        # Enhanced gold wave with gradient effect
        for i in range(3):
            y_pos = 990 + i
            alpha = 200 - (i * 40)
            draw.line([(100, y_pos), (300, y_pos-10), (500, y_pos)], fill=(255, 215, 0, alpha), width=2)
        # Main gold wave
        draw.line([(100, 990), (300, 980), (500, 990)], fill='#FFD700', width=3)

        # Sponsor boxes with enhanced positioning and HIGH QUALITY logos
        self._add_high_quality_sponsor_logos(draw, badge_img, sponsor_font)

        # Professional footer with organization contact - original positioning
        # Enhanced gold wave
        for i in range(2):
            y_pos = 1160 + i
            alpha = 200 - (i * 50)
            draw.line([(100, y_pos), (300, y_pos+10), (500, y_pos)], fill=(255, 215, 0, alpha), width=2)
        draw.line([(100, 1160), (300, 1170), (500, 1160)], fill='#FFD700', width=3)

        # Get primary organization contact from system with enhanced visibility
        footer_text = self._get_primary_organization_contact()
        draw.text((300, 1190), footer_text, fill='white',
                 font=footer_font, anchor='mm')

        # Save badge with HIGHEST QUALITY settings
        timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
        filename = f"badge_{self.participant.id}_{timestamp}.png"

        # Ensure directory exists
        badge_dir = os.path.join(settings.MEDIA_ROOT, 'generated_badges')
        os.makedirs(badge_dir, exist_ok=True)

        badge_path = os.path.join(badge_dir, filename)
        # ULTRA HIGH QUALITY: PNG with no compression, maximum DPI for print quality
        badge_img.save(badge_path, 'PNG', optimize=False, compress_level=0, dpi=(1200, 1200))

        # Save to model
        self.badge_image.name = f'generated_badges/{filename}'
        self.is_generated = True
        self.generated_at = timezone.now()
        self.save()

        print(f"✅ ENHANCED HIGH-QUALITY BADGE - Dimensions: ({width}, {height})")
        print(f"   🎯 IMPROVEMENTS: Larger fonts, better visibility, enhanced quality!")
        print(f"   📏 DPI: 1200x1200 for print quality")
        print(f"   🔤 Enhanced text sizes with original positioning preserved")
        print(f"   🖼️ Advanced logo processing with natural color preservation")

        return badge_img

    def _draw_text_with_glow(self, position, text, fill, font, draw, letter_spacing=0, weight='normal'):
        """Draw text with soft glow effect and letter spacing like SVG filter"""
        x, y = position

        # Apply letter spacing if specified
        if letter_spacing > 0:
            # Calculate total width with spacing
            total_width = 0
            char_widths = []
            for char in text:
                bbox = draw.textbbox((0, 0), char, font=font)
                char_width = bbox[2] - bbox[0]
                char_widths.append(char_width)
                total_width += char_width + letter_spacing
            total_width -= letter_spacing  # Remove last spacing

            # Draw each character with spacing
            start_x = x - total_width // 2
            current_x = start_x

            for i, char in enumerate(text):
                if char == ' ':
                    current_x += char_widths[i] + letter_spacing
                    continue

                # Soft glow effect (feGaussianBlur equivalent)
                for glow_offset in [(2, 2), (1, 1), (-1, -1), (-2, -2), (0, 2), (2, 0), (0, -2), (-2, 0)]:
                    glow_x = current_x + glow_offset[0]
                    glow_y = y + glow_offset[1]
                    glow_color = fill if isinstance(fill, str) else (fill[0], fill[1], fill[2], 50)
                    if isinstance(glow_color, str):
                        if glow_color == 'white':
                            glow_color = (255, 255, 255, 50)
                        elif glow_color == '#FFD700':
                            glow_color = (255, 215, 0, 50)
                    draw.text((glow_x, glow_y), char, fill=glow_color, font=font, anchor='mm')

                # Main character
                draw.text((current_x, y), char, fill=fill, font=font, anchor='mm')
                current_x += char_widths[i] + letter_spacing
        else:
            # No letter spacing - standard text with glow
            # Soft glow effect
            for glow_offset in [(2, 2), (1, 1), (-1, -1), (-2, -2), (0, 2), (2, 0), (0, -2), (-2, 0)]:
                glow_x = x + glow_offset[0]
                glow_y = y + glow_offset[1]
                glow_color = fill if isinstance(fill, str) else (fill[0], fill[1], fill[2], 50)
                if isinstance(glow_color, str):
                    if glow_color == 'white':
                        glow_color = (255, 255, 255, 50)
                    elif glow_color == '#FFD700':
                        glow_color = (255, 215, 0, 50)
                draw.text((glow_x, glow_y), text, fill=glow_color, font=font, anchor='mm')

            # Main text
            draw.text((x, y), text, fill=fill, font=font, anchor='mm')

    def _get_primary_organization_contact(self):
        """Get primary organization contact information from system"""
        try:
            # Try to get primary organization from event
            if hasattr(self.participant.event, 'primary_organization') and self.participant.event.primary_organization:
                org = self.participant.event.primary_organization
                website = getattr(org, 'website', None) or getattr(org, 'url', None)
                email = getattr(org, 'email', None) or getattr(org, 'contact_email', None)

                if website and email:
                    return f"{website} | {email}"
                elif website:
                    return website
                elif email:
                    return email
                else:
                    return getattr(org, 'name', 'Ministry of Education FDRE')

            # Try to get from event organizer
            if hasattr(self.participant.event, 'organizer') and self.participant.event.organizer:
                org = self.participant.event.organizer
                website = getattr(org, 'website', None)
                email = getattr(org, 'email', None)

                if website and email:
                    return f"{website} | {email}"
                elif website:
                    return website
                elif email:
                    return email

            # Default fallback
            return "www.moe.gov.et | <EMAIL>"

        except Exception as e:
            print(f"❌ Error getting organization contact: {e}")
            return "www.moe.gov.et | <EMAIL>"

    def _add_high_quality_sponsor_logos(self, draw, badge_img, sponsor_font):
        """Add sponsor logos with HIGHEST QUALITY processing"""
        # Get sponsors from event
        sponsors_data = []
        if hasattr(self.participant.event, 'sponsors') and self.participant.event.sponsors.exists():
            for sponsor in self.participant.event.sponsors.all()[:3]:
                sponsors_data.append({
                    'name': sponsor.name,
                    'logo': sponsor.logo if hasattr(sponsor, 'logo') else None
                })

        # Ensure we have exactly 3 sponsors
        while len(sponsors_data) < 3:
            sponsors_data.append({'name': "Partner", 'logo': None})
        sponsors_data = sponsors_data[:3]

        # Enhanced sponsor positions - original positioning with better quality
        sponsor_positions = [
            (100, 1020, 220, 1100),  # Original positions with enhanced quality
            (240, 1020, 360, 1100),
            (380, 1020, 500, 1100)
        ]

        for i, (x1, y1, x2, y2) in enumerate(sponsor_positions):
            sponsor = sponsors_data[i]
            sponsor_name = sponsor['name']
            sponsor_logo = sponsor['logo']

            # Enhanced sponsor box with gradient effect - original positioning
            draw.rounded_rectangle([x1-2, y1-2, x2+2, y2+2], radius=12,
                                 fill=(255, 215, 0, 20), outline='#FFD700', width=2)
            draw.rounded_rectangle([x1, y1, x2, y2], radius=10,
                                 fill=(255, 255, 255, 15), outline='#FFEC8B', width=1)

            # Try to load and display sponsor logo with HIGHEST QUALITY
            logo_loaded = False
            if sponsor_logo:
                try:
                    from PIL import Image, ImageEnhance
                    logo_img = Image.open(sponsor_logo.path)

                    # Convert to RGBA and enhance quality
                    if logo_img.mode != 'RGBA':
                        logo_img = logo_img.convert('RGBA')

                    # ADVANCED white background removal preserving natural colors
                    data = logo_img.getdata()
                    new_data = []
                    for item in data:
                        r, g, b = item[:3]
                        alpha = item[3] if len(item) == 4 else 255

                        # Only remove pure white or very close to white backgrounds
                        # Preserve natural colors and gradients
                        if (r >= 250 and g >= 250 and b >= 250 and
                            abs(r - g) <= 5 and abs(g - b) <= 5 and abs(r - b) <= 5):
                            # Make pure white transparent
                            new_data.append((255, 255, 255, 0))
                        else:
                            # Keep all other colors including off-whites and natural tones
                            new_data.append((r, g, b, alpha))
                    logo_img.putdata(new_data)

                    # Preserve aspect ratio with HIGH quality - original sizing
                    original_width, original_height = logo_img.size
                    max_width = (x2 - x1) - 30  # Original padding
                    max_height = (y2 - y1) - 40  # Original padding

                    scale_w = max_width / original_width
                    scale_h = max_height / original_height
                    scale = min(scale_w, scale_h)

                    new_width = int(original_width * scale)
                    new_height = int(original_height * scale)

                    # High-quality resize with anti-aliasing
                    logo_img = logo_img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # Apply enhancement for better quality
                    enhancer = ImageEnhance.Sharpness(logo_img)
                    logo_img = enhancer.enhance(1.1)

                    # Center logo in box
                    logo_x = x1 + (x2 - x1 - new_width) // 2
                    logo_y = y1 + (y2 - y1 - new_height) // 2 - 10
                    badge_img.paste(logo_img, (logo_x, logo_y), logo_img)

                    logo_loaded = True
                    print(f"✅ High-quality sponsor logo loaded: {sponsor_name}")
                except Exception as e:
                    print(f"❌ Error loading sponsor logo for {sponsor_name}: {e}")

            # Add sponsor name with enhanced styling
            center_x = (x1 + x2) // 2
            text_y = y1 + (y2 - y1) * 0.85 if logo_loaded else y1 + (y2 - y1) // 2

            # Text shadow for better readability
            draw.text((center_x+1, text_y+1), sponsor_name, fill=(0, 0, 0, 100),
                     font=sponsor_font, anchor='mm')
            # Main text
            draw.text((center_x, text_y), sponsor_name, fill='white',
                     font=sponsor_font, anchor='mm')

    def _add_exact_gold_pattern(self, draw, width, height):
        """Add subtle gold pattern overlay like SVG"""
        # Add subtle gold hair pattern - simplified to avoid float errors
        for y in range(0, height, 40):
            for x in range(0, width, 150):
                # Create simple wave pattern
                for i in range(0, 150, 10):
                    wave_y = y + 20 + (i % 20) - 10  # Simple wave without math.sin
                    if 0 <= wave_y < height and 0 <= x + i < width:
                        draw.point((x + i, wave_y), fill=(255, 215, 0, 64))  # Gold with opacity

    def _add_exact_sponsor_logos(self, draw, badge_img, sponsor_font):
        """Add sponsor logos with exact positioning and real logos"""
        # Get sponsors from event
        sponsors_data = []
        if hasattr(self.participant.event, 'sponsors') and self.participant.event.sponsors.exists():
            for sponsor in self.participant.event.sponsors.all()[:3]:
                sponsors_data.append({
                    'name': sponsor.name,
                    'logo': sponsor.logo if hasattr(sponsor, 'logo') else None
                })

        # Ensure we have exactly 3 sponsors
        while len(sponsors_data) < 3:
            sponsors_data.append({'name': "Partner", 'logo': None})
        sponsors_data = sponsors_data[:3]

        # Adjusted sponsor positions to avoid overlap
        sponsor_positions = [
            (100, 1050, 220, 1130),  # x="100" width="120" (moved down)
            (240, 1050, 360, 1130),  # x="240" width="120" (moved down)
            (380, 1050, 500, 1130)   # x="380" width="120" (moved down)
        ]

        for i, (x1, y1, x2, y2) in enumerate(sponsor_positions):
            sponsor = sponsors_data[i]
            sponsor_name = sponsor['name']
            sponsor_logo = sponsor['logo']

            # Draw sponsor box with exact styling
            draw.rounded_rectangle([x1, y1, x2, y2], radius=10,
                                 fill=(255, 255, 255, 18), outline='#FFD700', width=1)

            # Try to load and display sponsor logo
            logo_loaded = False
            if sponsor_logo:
                try:
                    from PIL import Image
                    logo_img = Image.open(sponsor_logo.path)

                    # Convert to RGBA and remove white background
                    if logo_img.mode != 'RGBA':
                        logo_img = logo_img.convert('RGBA')

                    # ADVANCED white background removal preserving natural colors
                    data = logo_img.getdata()
                    new_data = []
                    for item in data:
                        r, g, b = item[:3]
                        alpha = item[3] if len(item) == 4 else 255

                        # Only remove pure white or very close to white backgrounds
                        # Preserve natural colors and gradients
                        if (r >= 250 and g >= 250 and b >= 250 and
                            abs(r - g) <= 5 and abs(g - b) <= 5 and abs(r - b) <= 5):
                            # Make pure white transparent
                            new_data.append((255, 255, 255, 0))
                        else:
                            # Keep all other colors including off-whites and natural tones
                            new_data.append((r, g, b, alpha))
                    logo_img.putdata(new_data)

                    # Preserve aspect ratio
                    original_width, original_height = logo_img.size
                    max_width = (x2 - x1) - 20
                    max_height = (y2 - y1) - 30

                    scale_w = max_width / original_width
                    scale_h = max_height / original_height
                    scale = min(scale_w, scale_h)

                    new_width = int(original_width * scale)
                    new_height = int(original_height * scale)

                    logo_img = logo_img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # Center logo in box
                    logo_x = x1 + (x2 - x1 - new_width) // 2
                    logo_y = y1 + (y2 - y1 - new_height) // 2 - 10
                    badge_img.paste(logo_img, (logo_x, logo_y), logo_img)

                    logo_loaded = True
                except Exception as e:
                    print(f"❌ Error loading sponsor logo for {sponsor_name}: {e}")

            # Add sponsor name at exact position (y=1040 from SVG)
            center_x = (x1 + x2) // 2
            text_y = y1 + (y2 - y1) * 0.6 if logo_loaded else y1 + (y2 - y1) // 2
            draw.text((center_x, text_y), sponsor_name, fill='white',
                     font=sponsor_font, anchor='mm')

    def _add_svg_gradient_background(self, draw, width, height):
        """Add gradient background like SVG template"""
        # Create gradient from #002D72 to #021B3A
        for y in range(height):
            progress = y / height
            # Interpolate between colors
            r = int(0 + (2 - 0) * progress)
            g = int(45 + (27 - 45) * progress)
            b = int(114 + (58 - 114) * progress)
            draw.line([(0, y), (width, y)], fill=(r, g, b))

    def _add_svg_decorative_elements(self, draw, width, height):
        """Add decorative dots like SVG template"""
        # Left side dots
        dots = [(50, 150), (70, 170), (90, 190), (110, 210), (130, 230)]
        for x, y in dots:
            draw.ellipse([x-3, y-3, x+3, y+3], fill=(255, 255, 255, 38))  # 15% opacity

        # Right side dots
        dots = [(550, 180), (530, 200), (510, 220), (490, 240)]
        for x, y in dots:
            draw.ellipse([x-3, y-3, x+3, y+3], fill=(255, 255, 255, 38))  # 15% opacity

    def _add_svg_top_ribbon(self, draw, width, height):
        """Add top decorative ribbon like SVG template"""
        # Main ribbon
        points = [(0, 0), (width, 0), (width, 120), (0, 300)]
        draw.polygon(points, fill=(0, 75, 145, 102))  # #004B91 with 40% opacity

        # Secondary ribbon
        points = [(0, 300), (width, 120), (width, 140), (0, 320)]
        draw.polygon(points, fill=(0, 75, 145, 77))  # #004B91 with 30% opacity

    def _add_svg_ministry_logo(self, draw, width, height):
        """Ministry logo removed as requested"""
        # No logo will be drawn
        pass

    def _add_svg_ministry_title(self, draw, width, height):
        """Add ministry title like SVG template"""
        try:
            title_font = ImageFont.truetype("arial.ttf", 24)
            subtitle_font = ImageFont.truetype("arial.ttf", 20)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()

        # Main title
        title = "MINISTRY OF EDUCATION OF FDRE"
        draw.text((width//2, 165), title, fill='white', font=title_font, anchor='mm')

        # Subtitle
        subtitle = "HIGHER EDUCATION DEVELOPMENT SECTOR"
        draw.text((width//2, 195), subtitle, fill='white', font=subtitle_font, anchor='mm')

    def _add_svg_theme(self, draw, width, height):
        """Add theme section like SVG template"""
        # Background rectangle
        draw.rounded_rectangle([150, 210, 450, 240], radius=15, fill=(255, 255, 255, 38))

        try:
            theme_font = ImageFont.truetype("arial.ttf", 18)
        except:
            theme_font = ImageFont.load_default()

        # Use actual event name or default theme
        if self.participant.event:
            theme_text = self.participant.event.name.upper()
            if len(theme_text) > 35:
                theme_text = theme_text[:32] + "..."
        else:
            theme_text = "HIGHER EDUCATION FOR HIGHER IMPACT"

        draw.text((width//2, 230), theme_text, fill='white', font=theme_font, anchor='mm')

    def _add_svg_photo_area(self, draw, width, height):
        """Add photo area with real participant photos"""
        # Draw photo frame without white background
        draw.rounded_rectangle([150, 260, 450, 540], radius=20,
                             fill=None, outline=(255, 255, 255, 140), width=2)
        draw.rounded_rectangle([175, 285, 425, 515], radius=15,
                             fill=None, outline=(255, 255, 255, 100), width=1)

        # Try to load actual participant photo if it exists
        photo_loaded = False
        if hasattr(self.participant, 'profile_photo') and self.participant.profile_photo:
            try:
                from PIL import Image
                participant_photo = Image.open(self.participant.profile_photo.path)
                # Resize photo to fit the frame
                photo_size = (240, 220)  # Frame size minus padding
                participant_photo = participant_photo.resize(photo_size, Image.Resampling.LANCZOS)

                # Paste photo in the center of the frame
                photo_x = 180  # 175 + 5 padding
                photo_y = 295  # 285 + 10 padding
                # Create a temporary image for the photo with rounded corners
                mask = Image.new('L', photo_size, 0)
                mask_draw = ImageDraw.Draw(mask)
                mask_draw.rounded_rectangle([0, 0, photo_size[0], photo_size[1]], radius=10, fill=255)

                # Create badge image from draw object
                badge_img = draw._image
                badge_img.paste(participant_photo, (photo_x, photo_y), mask)
                photo_loaded = True
                print(f"✅ Loaded participant photo: {self.participant.profile_photo.path}")
            except Exception as e:
                print(f"❌ Error loading participant photo: {e}")
                photo_loaded = False

        # If no photo loaded, show event theme/motto
        if not photo_loaded:
            try:
                theme_font = ImageFont.truetype("arial.ttf", 20)
            except:
                theme_font = ImageFont.load_default()

            # Get event theme/motto, default to "Higher education for higher impact"
            event_theme = getattr(self.participant.event, 'theme', None) or \
                         getattr(self.participant.event, 'motto', None) or \
                         "HIGHER EDUCATION\nFOR HIGHER IMPACT"

            center_x, center_y = 300, 400
            draw.text((center_x, center_y), event_theme, fill='white',
                     font=theme_font, anchor='mm', align='center')

    def _add_svg_participant_info(self, draw, width, height):
        """Add participant information like SVG template"""
        try:
            name_font = ImageFont.truetype("arial.ttf", 34)  # Larger name
            position_font = ImageFont.truetype("arial.ttf", 26)  # Larger position
            institution_font = ImageFont.truetype("arial.ttf", 24)  # Larger institution
        except:
            name_font = ImageFont.load_default()
            position_font = ImageFont.load_default()
            institution_font = ImageFont.load_default()

        # Full name - use actual participant data
        full_name = self.participant.full_name
        if len(full_name) > 30:
            full_name = full_name[:27] + "..."
        draw.text((width//2, 560), full_name, fill='white', font=name_font, anchor='mm')

        # Position - use actual participant data
        position = getattr(self.participant, 'position', None)
        if not position:
            position = getattr(self.participant, 'job_title', None)
        if not position:
            position = getattr(self.participant, 'title', None)
        if not position:
            position = "Participant"

        if len(position) > 35:
            position = position[:32] + "..."
        draw.text((width//2, 590), position, fill=(255, 255, 255, 221), font=position_font, anchor='mm')

        # Institution - use actual participant data
        institution = getattr(self.participant, 'institution_name', None)
        if not institution:
            institution = getattr(self.participant, 'organization', None)
        if not institution:
            institution = getattr(self.participant, 'affiliation', None)
        if not institution:
            institution = "Institution"

        if len(institution) > 40:
            institution = institution[:37] + "..."
        draw.text((width//2, 620), institution, fill=(255, 255, 255, 170), font=institution_font, anchor='mm')

    def _add_svg_participant_type(self, draw, width, height):
        """Add participant type badge like SVG template"""
        # Get participant type
        if self.participant.participant_type:
            ptype = self.participant.participant_type.name.upper()
        else:
            ptype = "PARTICIPANT"

        if len(ptype) > 25:
            ptype = ptype[:22] + "..."

        # Different colors and styles for different participant types
        type_styles = {
            'VIP': {'bg': '#FFD700', 'text': '#000000', 'size': 22},  # Gold background, black text
            'SPEAKER': {'bg': '#FF6B35', 'text': '#FFFFFF', 'size': 22},  # Orange background
            'DELEGATE': {'bg': '#4ECDC4', 'text': '#FFFFFF', 'size': 20},  # Teal background
            'ORGANIZER': {'bg': '#45B7D1', 'text': '#FFFFFF', 'size': 20},  # Light Blue background
            'GUEST': {'bg': '#96CEB4', 'text': '#FFFFFF', 'size': 20},  # Light Green background
        }

        style = type_styles.get(ptype, {'bg': 'white', 'text': '#002D72', 'size': 20})

        # Badge background with type-specific color
        draw.rounded_rectangle([150, 650, 450, 700], radius=25,
                             fill=style['bg'], outline='#002D72', width=2)

        try:
            type_font = ImageFont.truetype("arial.ttf", style['size'])
        except:
            type_font = ImageFont.load_default()

        draw.text((300, 675), ptype, fill=style['text'], font=type_font, anchor='mm')

    def _add_svg_qr_code(self, draw, width, height, qr_img):
        """Add QR code like SVG template"""
        # QR code frame without white background
        draw.rounded_rectangle([175, 720, 425, 970], radius=15,
                             fill=None, outline=(255, 255, 255, 100), width=1)

        # QR code background - keep white for QR readability
        draw.rectangle([200, 745, 400, 945], fill='white')

        # Note: QR code will be pasted in the main generate_badge method
        # This just creates the frame and background

        # QR code label - updated for attendance and event details
        try:
            qr_font = ImageFont.truetype("arial.ttf", 14)
        except:
            qr_font = ImageFont.load_default()

        # Multi-line label for attendance and event details
        label_line1 = "SCAN FOR ATTENDANCE"
        label_line2 = "& EVENT DETAILS"

        draw.text((300, 975), label_line1, fill='white', font=qr_font, anchor='mm')
        draw.text((300, 995), label_line2, fill=(255, 255, 255, 170), font=qr_font, anchor='mm')

    def _add_svg_sponsors(self, draw, width, height):
        """Add sponsors section like SVG template"""
        # Separator line
        draw.line([(100, 1000), (500, 1000)], fill=(255, 255, 255, 77), width=1)

        try:
            sponsor_font = ImageFont.truetype("arial.ttf", 18)
            sponsor_label_font = ImageFont.truetype("arial.ttf", 14)
        except:
            sponsor_font = ImageFont.load_default()
            sponsor_label_font = ImageFont.load_default()

        # Sponsors title
        draw.text((300, 1030), "SPONSORED BY", fill=(255, 255, 255, 204),
                 font=sponsor_font, anchor='mm')

        # Get real sponsors with logos from event
        sponsors_data = []
        try:
            from events.models import Event
            event = Event.objects.filter(is_active=True).first()
            if event:
                event_sponsors = event.sponsors.filter(is_active=True)[:3]
                for sponsor in event_sponsors:
                    sponsors_data.append({
                        'name': sponsor.name,
                        'logo': sponsor.logo if sponsor.logo else None
                    })
        except Exception as e:
            print(f"Error loading sponsors: {e}")

        # Default sponsors if no real ones found
        if not sponsors_data:
            sponsors_data = [
                {'name': "Ministry of Education", 'logo': None},
                {'name': "University of Gondar", 'logo': None},
                {'name': "Partners", 'logo': None}
            ]

        # Ensure we have exactly 3 sponsors
        while len(sponsors_data) < 3:
            sponsors_data.append({'name': "Partner", 'logo': None})
        sponsors_data = sponsors_data[:3]

        # Sponsor boxes with real data and logos
        sponsor_positions = [
            (100, 1050, 220, 1130),
            (240, 1050, 360, 1130),
            (380, 1050, 500, 1130)
        ]

        for i, (x1, y1, x2, y2) in enumerate(sponsor_positions):
            sponsor = sponsors_data[i]
            sponsor_name = sponsor['name']
            sponsor_logo = sponsor['logo']

            if len(sponsor_name) > 15:
                sponsor_name = sponsor_name[:12] + "..."

            # Draw sponsor box without white background
            draw.rounded_rectangle([x1, y1, x2, y2], radius=10,
                                 fill=None, outline=(255, 255, 255, 100), width=1)

            # Try to load and display sponsor logo
            logo_loaded = False
            if sponsor_logo:
                try:
                    from PIL import Image
                    logo_img = Image.open(sponsor_logo.path)

                    # Convert to RGBA for transparency handling
                    if logo_img.mode != 'RGBA':
                        logo_img = logo_img.convert('RGBA')

                    # ADVANCED white background removal preserving natural colors
                    data = logo_img.getdata()
                    new_data = []
                    for item in data:
                        r, g, b = item[:3]
                        alpha = item[3] if len(item) == 4 else 255

                        # Only remove pure white or very close to white backgrounds
                        # Preserve natural colors and gradients
                        if (r >= 250 and g >= 250 and b >= 250 and
                            abs(r - g) <= 5 and abs(g - b) <= 5 and abs(r - b) <= 5):
                            # Make pure white transparent
                            new_data.append((255, 255, 255, 0))
                        else:
                            # Keep all other colors including off-whites and natural tones
                            new_data.append((r, g, b, alpha))
                    logo_img.putdata(new_data)

                    # Keep original aspect ratio - don't force reshape
                    original_width, original_height = logo_img.size
                    max_width = (x2 - x1) - 20  # Leave some padding
                    max_height = (y2 - y1) - 40  # Leave space for text

                    # Calculate scale to fit within bounds while preserving aspect ratio
                    scale_w = max_width / original_width
                    scale_h = max_height / original_height
                    scale = min(scale_w, scale_h)

                    new_width = int(original_width * scale)
                    new_height = int(original_height * scale)

                    # Resize with high quality while preserving shape
                    logo_img = logo_img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # Paste logo in center of box with transparency
                    badge_img = draw._image
                    logo_x = x1 + (x2 - x1 - new_width) // 2
                    logo_y = y1 + (y2 - y1 - new_height) // 2 - 15  # Slightly higher for text space
                    badge_img.paste(logo_img, (logo_x, logo_y), logo_img)  # Use logo as mask for transparency

                    # Add sponsor name below logo
                    center_x = (x1 + x2) // 2
                    text_y = y1 + (y2 - y1) * 0.9
                    try:
                        name_font = ImageFont.truetype("arial.ttf", 10)
                    except:
                        name_font = ImageFont.load_default()
                    draw.text((center_x, text_y), sponsor_name, fill='white',
                             font=name_font, anchor='mm')
                    logo_loaded = True
                    print(f"✅ Loaded sponsor logo: {sponsor_name} (preserved aspect ratio)")
                except Exception as e:
                    print(f"❌ Error loading sponsor logo for {sponsor_name}: {e}")
                    logo_loaded = False

            # If no logo loaded, show sponsor name only
            if not logo_loaded:
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                try:
                    text_font = ImageFont.truetype("arial.ttf", 12)
                except:
                    text_font = ImageFont.load_default()
                draw.text((center_x, center_y), sponsor_name, fill='white',
                         font=text_font, anchor='mm')

    def _add_svg_footer(self, draw, width, height):
        """Add footer like SVG template"""
        # Separator line
        draw.line([(100, 1150), (500, 1150)], fill=(255, 255, 255, 77), width=1)

        try:
            footer_font = ImageFont.truetype("arial.ttf", 16)
            social_font = ImageFont.truetype("arial.ttf", 14)
        except:
            footer_font = ImageFont.load_default()
            social_font = ImageFont.load_default()

        # Contact info - use actual event contact or default
        if self.participant.event and hasattr(self.participant.event, 'contact_email'):
            contact_info = f"www.uog.edu.et | {self.participant.event.contact_email}"
        else:
            contact_info = "www.uog.edu.et | <EMAIL>"

        draw.text((300, 1180), contact_info,
                 fill='white', font=footer_font, anchor='mm')

        # Social media
        draw.text((300, 1210), "Follow us: @UoGEthiopia",
                 fill=(255, 255, 255, 170), font=social_font, anchor='mm')

    # OLD COMPLEX BADGE GENERATION METHODS REMOVED
    # The badge generation now uses the simplified SVG-style approach
    # All old methods (_add_premium_*, _add_dark_*, etc.) have been removed
    # for cleaner, more maintainable code that matches the exact SVG template

    # All old complex badge generation methods have been removed for simplicity
    # The new SVG-style badge generation is much cleaner and easier to maintain

    def _old_methods_removed_placeholder(self):
        """This method has been removed - using simplified SVG generation instead"""
        pass
        # Banner height (35mm - ending at photo midpoint)
        banner_height = int(35 * 11.81)  # ~413 pixels
        corner_radius = int(4 * 11.81)  # 4mm corner radius

        # Create faded blue gradient banner - EXACT SVG implementation
        for y in range(banner_height):
            progress = y / banner_height
            if progress <= 0.5:
                # Gradient from #1e3a8a to #3b82f6 (first 50%)
                r = int(30 + (59 - 30) * (progress * 2))
                g = int(58 + (130 - 58) * (progress * 2))
                b = int(138 + (246 - 138) * (progress * 2))
                draw.line([(0, y), (width, y)], fill=(r, g, b))
            else:
                # Complete fade out after 50% (stop-opacity="0")
                break

        # Add wave pattern overlay only on visible banner area
        self._add_wave_pattern(draw, width, int(banner_height * 0.5))

    def _add_wave_pattern(self, draw, width, height):
        """Add decorative wave pattern"""
        # Wave pattern (40x20mm pattern)
        wave_width = int(40 * 11.81)  # ~472 pixels
        wave_height = int(20 * 11.81)  # ~236 pixels

        for x in range(0, width, wave_width):
            for y in range(0, height, wave_height):
                # Draw wave using bezier-like curves
                wave_y = y + wave_height // 2
                for i in range(0, wave_width, 4):
                    wave_x = x + i
                    if wave_x < width:
                        # Create wave effect
                        offset = int(5 * math.sin(i * math.pi / (wave_width / 4)))
                        temp_img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
                        temp_draw = ImageDraw.Draw(temp_img)
                        temp_draw.point((wave_x, wave_y + offset), fill=(96, 165, 250, 38))  # #60a5fa with opacity
                        badge_img = draw._image
                        badge_img.paste(temp_img, (0, 0), temp_img)

    def _add_premium_event_header(self, draw, width, height):
        """Add event header with Georgia serif font"""
        try:
            # Load Georgia serif fonts
            title_font = ImageFont.truetype("georgia.ttf", 57)  # 4.8mm equivalent
            subtitle_font = ImageFont.truetype("georgia.ttf", 36)  # 3mm equivalent
        except:
            try:
                title_font = ImageFont.truetype("times.ttf", 57)
                subtitle_font = ImageFont.truetype("times.ttf", 36)
            except:
                title_font = ImageFont.load_default()
                subtitle_font = ImageFont.load_default()

        # Event title (y=12mm)
        event_name = self.participant.event.name.upper()
        if len(event_name) > 25:
            event_name = event_name[:22] + "..."

        title_y = int(12 * 11.81)  # 12mm from top
        title_bbox = draw.textbbox((0, 0), event_name, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (width - title_width) // 2

        draw.text((title_x, title_y), event_name, fill='white', font=title_font)

        # Event dates/location (y=17mm)
        event = self.participant.event
        if event.start_date and event.end_date:
            if event.start_date == event.end_date:
                date_text = f"{event.start_date.strftime('%B %d, %Y').upper()}"
            else:
                date_text = f"{event.start_date.strftime('%B %d').upper()}-{event.end_date.strftime('%d, %Y').upper()}"
        else:
            date_text = "UNIVERSITY OF GONDAR"

        subtitle_y = int(17 * 11.81)  # 17mm from top
        subtitle_bbox = draw.textbbox((0, 0), date_text, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = (width - subtitle_width) // 2

        draw.text((subtitle_x, subtitle_y), date_text, fill='#bfdbfe', font=subtitle_font)

    def _add_premium_photo(self, draw, width, height):
        """Add participant photo with wave-inspired frame - EXACT SVG match"""
        # Photo position - EXACT SVG coordinates (center x=45mm, center y=35mm)
        photo_center_x = int(45 * 11.81)  # ~531 pixels (exact center)
        photo_center_y = int(35 * 11.81)  # ~413 pixels (exact center at banner end)
        photo_radius = int(16 * 11.81)    # 16mm radius (~189 pixels)
        inner_radius = int(14 * 11.81)    # 14mm inner radius (~165 pixels)

        # Outer circle with gold gradient stroke (dashed)
        self._draw_dashed_circle(draw, photo_center_x, photo_center_y, photo_radius,
                                '#d4af37', '#fde047', dash_length=int(6 * 11.81), gap_length=int(2 * 11.81))

        # Inner circle background
        draw.ellipse([photo_center_x - inner_radius, photo_center_y - inner_radius,
                     photo_center_x + inner_radius, photo_center_y + inner_radius],
                    fill='#f0f9ff')

        # Try to load participant photo
        if self.participant.profile_photo:
            try:
                photo_img = Image.open(self.participant.profile_photo.path)
                photo_img = photo_img.convert('RGB')
                photo_size = inner_radius * 2
                photo_img = photo_img.resize((photo_size, photo_size), Image.Resampling.LANCZOS)

                # Create circular mask
                mask = Image.new('L', (photo_size, photo_size), 0)
                mask_draw = ImageDraw.Draw(mask)
                mask_draw.ellipse([0, 0, photo_size, photo_size], fill=255)

                # Apply mask and paste
                output = Image.new('RGB', (photo_size, photo_size), '#f0f9ff')
                output.paste(photo_img, (0, 0))
                output.putalpha(mask)

                badge_img = draw._image
                badge_img.paste(output, (photo_center_x - inner_radius, photo_center_y - inner_radius), output)

            except Exception:
                self._draw_premium_photo_placeholder(draw, photo_center_x, photo_center_y, inner_radius)
        else:
            self._draw_premium_photo_placeholder(draw, photo_center_x, photo_center_y, inner_radius)

    def _draw_dashed_circle(self, draw, center_x, center_y, radius, color1, color2, dash_length, gap_length):
        """Draw a dashed circle with gradient colors"""
        circumference = 2 * math.pi * radius
        total_dash_gap = dash_length + gap_length
        num_dashes = int(circumference / total_dash_gap)

        for i in range(num_dashes):
            start_angle = (i * total_dash_gap / circumference) * 360
            end_angle = ((i * total_dash_gap + dash_length) / circumference) * 360

            # Create gradient effect by alternating colors
            color = color1 if i % 2 == 0 else color2

            # Draw arc segment
            bbox = [center_x - radius, center_y - radius, center_x + radius, center_y + radius]
            draw.arc(bbox, start_angle, end_angle, fill=color, width=int(1.5 * 11.81))

    def _draw_premium_photo_placeholder(self, draw, center_x, center_y, radius):
        """Draw premium photo placeholder"""
        try:
            placeholder_font = ImageFont.truetype("arial.ttf", 21)  # 1.8mm equivalent
        except:
            placeholder_font = ImageFont.load_default()

        placeholder_text = "PORTRAIT"
        text_bbox = draw.textbbox((0, 0), placeholder_text, font=placeholder_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        text_x = center_x - text_width // 2
        text_y = center_y - text_height // 2

        draw.text((text_x, text_y), placeholder_text, fill='#93c5fd', font=placeholder_font)

    def _add_premium_participant_info(self, draw, width, height):
        """Add participant information with Georgia serif font - EXACT SVG match"""
        try:
            name_font = ImageFont.truetype("georgia.ttf", 69)  # 5.8mm equivalent
        except:
            try:
                name_font = ImageFont.truetype("times.ttf", 69)
            except:
                name_font = ImageFont.load_default()

        # Participant name (y=58mm) - EXACT SVG positioning
        full_name = f"{self.participant.first_name} {self.participant.last_name}".upper()
        if len(full_name) > 20:
            full_name = full_name[:17] + "..."

        name_y = int(58 * 11.81)  # 58mm from top - exact SVG match
        center_x = width // 2

        # Use center anchor for exact SVG match
        draw.text((center_x, name_y), full_name, fill='#1e3a8a', font=name_font, anchor='mt')

    def _add_premium_institution_info(self, draw, width, height):
        """Add institution and position with Georgia serif font - EXACT SVG match"""
        try:
            institution_font = ImageFont.truetype("georgia.ttf", 45)  # 3.8mm equivalent
            position_font = ImageFont.truetype("georgia.ttf", 38)     # 3.2mm equivalent (italic)
        except:
            try:
                institution_font = ImageFont.truetype("times.ttf", 45)
                position_font = ImageFont.truetype("times.ttf", 38)
            except:
                institution_font = ImageFont.load_default()
                position_font = ImageFont.load_default()

        center_x = width // 2

        # Institution name (y=66mm) - EXACT SVG positioning
        institution = getattr(self.participant, 'institution_name', '') or "University of Gondar"
        if len(institution) > 25:
            institution = institution[:22] + "..."

        institution_y = int(66 * 11.81)  # 66mm from top - exact SVG match
        draw.text((center_x, institution_y), institution, fill='#1e40af', font=institution_font, anchor='mt')

        # Position (y=71mm) - EXACT SVG positioning with italic style
        position = getattr(self.participant, 'position', '') or "Faculty of Advanced Sciences"
        if len(position) > 30:
            position = position[:27] + "..."

        position_y = int(71 * 11.81)  # 71mm from top - exact SVG match
        draw.text((center_x, position_y), position, fill='#60a5fa', font=position_font, anchor='mt')

    def _add_premium_qr_code(self, draw, width, height, qr_img):
        """Add QR code at raised position with wave pattern border - EXACT SVG match"""
        # QR code position (x=30mm, y=80mm, 30x30mm) - EXACT SVG coordinates
        qr_x = int(30 * 11.81)      # 30mm from left - exact SVG match
        qr_y = int(80 * 11.81)      # 80mm from top - exact SVG match
        qr_size = int(30 * 11.81)   # 30mm size - exact SVG match

        # White background with gold border
        draw.rectangle([qr_x, qr_y, qr_x + qr_size, qr_y + qr_size],
                      fill='white', outline='#d4af37', width=int(1 * 11.81))

        # Add wave pattern border
        self._add_wave_border(draw, qr_x, qr_y, qr_size)

        # Resize and paste QR code
        qr_inner_size = qr_size - int(4 * 11.81)  # 2mm margin on each side
        qr_resized = qr_img.resize((qr_inner_size, qr_inner_size), Image.Resampling.LANCZOS)
        badge_img = draw._image
        badge_img.paste(qr_resized, (qr_x + int(2 * 11.81), qr_y + int(2 * 11.81)))

        # QR code label (y=113mm)
        try:
            qr_font = ImageFont.truetype("arial.ttf", 24)  # 2mm equivalent
        except:
            qr_font = ImageFont.load_default()

        qr_label = "Scan for profile"
        label_y = int(113 * 11.81)  # 113mm from top
        label_bbox = draw.textbbox((0, 0), qr_label, font=qr_font)
        label_width = label_bbox[2] - label_bbox[0]
        label_x = (width - label_width) // 2

        draw.text((label_x, label_y), qr_label, fill='#3b82f6', font=qr_font)

    def _add_wave_border(self, draw, x, y, size):
        """Add wave pattern border around QR code"""
        # Draw wave pattern around the border
        wave_stroke_width = int(1.5 * 11.81)
        for i in range(0, size, 8):
            # Top border
            wave_offset = int(2 * math.sin(i * math.pi / 20))
            draw.line([(x + i, y + wave_offset), (x + i + 4, y + wave_offset)],
                     fill='#60a5fa', width=wave_stroke_width)

            # Bottom border
            draw.line([(x + i, y + size + wave_offset), (x + i + 4, y + size + wave_offset)],
                     fill='#60a5fa', width=wave_stroke_width)

    def _add_premium_role_badge(self, draw, width, height):
        """Add keynote speaker badge with gold gradient - EXACT SVG match"""
        # Badge position (x=20mm, y=118mm, 50x8mm) - EXACT SVG coordinates
        badge_x = int(20 * 11.81)      # 20mm from left - exact SVG match
        badge_y = int(118 * 11.81)     # 118mm from top - exact SVG match
        badge_width = int(50 * 11.81)  # 50mm width - exact SVG match
        badge_height = int(8 * 11.81)  # 8mm height - exact SVG match

        # Create gold gradient background
        for y in range(badge_height):
            progress = y / badge_height
            # Gold gradient from #d4af37 to #fde047
            r = int(212 + (253 - 212) * progress)
            g = int(175 + (224 - 175) * progress)
            b = int(55 + (71 - 55) * progress)
            draw.line([(badge_x, badge_y + y), (badge_x + badge_width, badge_y + y)], fill=(r, g, b))

        # Round the corners
        corner_radius = int(4 * 11.81)
        draw.rounded_rectangle([badge_x, badge_y, badge_x + badge_width, badge_y + badge_height],
                             radius=corner_radius, fill=None, outline=None)

        # Badge text
        try:
            badge_font = ImageFont.truetype("georgia.ttf", 50)  # 4.2mm equivalent
        except:
            try:
                badge_font = ImageFont.truetype("times.ttf", 50)
            except:
                badge_font = ImageFont.load_default()

        # Get participant type - EXACT SVG match
        ptype = self.participant.participant_type.name.upper() if self.participant.participant_type else "KEYNOTE SPEAKER"

        # Center text exactly like SVG
        text_center_x = badge_x + badge_width // 2
        text_center_y = badge_y + badge_height // 2

        draw.text((text_center_x, text_center_y), ptype, fill='#1e3a8a', font=badge_font, anchor='mm')

    def _add_premium_sponsors(self, draw, width, height):
        """Add sponsor logos with wave separator"""
        try:
            from events.models import EventSponsor
            sponsors = EventSponsor.objects.filter(event=self.participant.event, is_active=True)[:3]
        except:
            sponsors = []

        # Wave separator (y=130mm)
        separator_y = int(130 * 11.81)  # 130mm from top
        self._draw_gold_wave_separator(draw, width, separator_y)

        # Sponsor logos (y=132mm, 20x10mm each)
        logo_y = int(132 * 11.81)      # 132mm from top
        logo_width = int(20 * 11.81)   # 20mm width
        logo_height = int(10 * 11.81)  # 10mm height

        # Calculate positions for 3 logos
        total_width = 3 * logo_width + 2 * int(3 * 11.81)  # logos + spacing
        start_x = (width - total_width) // 2

        logo_positions = [
            start_x,                                    # First logo
            start_x + logo_width + int(3 * 11.81),    # Second logo
            start_x + 2 * (logo_width + int(3 * 11.81)) # Third logo
        ]

        for i, sponsor in enumerate(sponsors):
            if i >= 3:  # Limit to 3 sponsors
                break

            logo_x = logo_positions[i]

            # Logo background
            draw.rounded_rectangle([logo_x, logo_y, logo_x + logo_width, logo_y + logo_height],
                                 radius=int(1 * 11.81), fill='#e0f2fe',
                                 outline='#1e3a8a', width=int(0.3 * 11.81))

            # Try to load sponsor logo
            if sponsor.logo:
                try:
                    logo_img = Image.open(sponsor.logo.path)
                    logo_img = logo_img.convert('RGBA')

                    # Resize maintaining aspect ratio
                    max_logo_width = logo_width - int(4 * 11.81)  # 2mm margin
                    max_logo_height = logo_height - int(4 * 11.81)  # 2mm margin
                    logo_img.thumbnail((max_logo_width, max_logo_height), Image.Resampling.LANCZOS)

                    # Center the logo
                    logo_paste_x = logo_x + (logo_width - logo_img.width) // 2
                    logo_paste_y = logo_y + (logo_height - logo_img.height) // 2

                    badge_img = draw._image
                    badge_img.paste(logo_img, (logo_paste_x, logo_paste_y), logo_img)

                except Exception:
                    # Fallback: draw sponsor name
                    self._draw_premium_sponsor_name(draw, sponsor, logo_x, logo_y, logo_width, logo_height)
            else:
                self._draw_premium_sponsor_name(draw, sponsor, logo_x, logo_y, logo_width, logo_height)

    def _draw_gold_wave_separator(self, draw, width, y):
        """Draw gold dashed wave separator"""
        # Draw wave using quadratic bezier-like curve
        wave_points = []
        for x in range(0, width, 4):
            # Create wave effect
            wave_offset = int(3 * math.sin(x * math.pi / 100))
            wave_points.append((x, y + wave_offset))

        # Draw dashed wave line
        dash_length = int(5 * 11.81)
        gap_length = int(2 * 11.81)

        for i in range(0, len(wave_points) - 1, dash_length + gap_length):
            end_idx = min(i + dash_length, len(wave_points) - 1)
            if end_idx > i:
                # Draw dash segment with gold gradient
                for j in range(i, end_idx):
                    if j + 1 < len(wave_points):
                        draw.line([wave_points[j], wave_points[j + 1]],
                                fill='#d4af37', width=int(0.8 * 11.81))

    def _draw_premium_sponsor_name(self, draw, sponsor, x, y, width, height):
        """Draw sponsor name as fallback"""
        try:
            sponsor_font = ImageFont.truetype("arial.ttf", 16)
        except:
            sponsor_font = ImageFont.load_default()

        # Truncate name if too long
        name = sponsor.name
        if len(name) > 8:
            name = name[:6] + ".."

        # Center text
        text_bbox = draw.textbbox((0, 0), name, font=sponsor_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        text_x = x + (width - text_width) // 2
        text_y = y + (height - text_height) // 2

        draw.text((text_x, text_y), name, fill='#1e3a8a', font=sponsor_font)

    def _add_template_border(self, draw, width, height):
        """Add border like SVG template"""
        # Convert mm to pixels (300 DPI: 1mm = ~11.81 pixels)
        border_width = int(1 * 11.81)  # 1mm border

        # Draw border
        draw.rectangle([border_width, border_width, width - border_width, height - border_width],
                      fill=None, outline='#dee2e6', width=4)

    def _add_event_header_template(self, draw, width, height):
        """Add event header with gradient like SVG template"""
        # Header height (15mm equivalent)
        header_height = int(15 * 11.81)  # ~177 pixels

        # Create gradient background (blue gradient)
        for y in range(header_height):
            progress = y / header_height
            # Blue gradient from #0056b3 to #003366
            r = int(0 + (0 * progress))
            g = int(86 + (51 - 86) * progress)
            b = int(179 + (102 - 179) * progress)
            draw.line([(0, y), (width, y)], fill=(r, g, b))

        # Load fonts
        try:
            title_font = ImageFont.truetype("arial.ttf", 54)  # Large title
            subtitle_font = ImageFont.truetype("arial.ttf", 26)  # Subtitle
        except:
            try:
                title_font = ImageFont.truetype("DejaVuSans-Bold.ttf", 54)
                subtitle_font = ImageFont.truetype("DejaVuSans.ttf", 26)
            except:
                title_font = ImageFont.load_default()
                subtitle_font = ImageFont.load_default()

        # Event title
        event_name = self.participant.event.name.upper()
        if len(event_name) > 25:
            event_name = event_name[:22] + "..."

        # Center the title
        title_bbox = draw.textbbox((0, 0), event_name, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (width - title_width) // 2
        title_y = int(10 * 11.81)  # 10mm from top

        draw.text((title_x, title_y), event_name, fill='white', font=title_font)

        # Event dates/location
        event = self.participant.event
        if event.start_date and event.end_date:
            if event.start_date == event.end_date:
                date_text = f"GONDAR | {event.start_date.strftime('%B %d, %Y').upper()}"
            else:
                date_text = f"GONDAR | {event.start_date.strftime('%b %d').upper()}-{event.end_date.strftime('%b %d, %Y').upper()}"
        else:
            date_text = "UNIVERSITY OF GONDAR"

        subtitle_bbox = draw.textbbox((0, 0), date_text, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = (width - subtitle_width) // 2
        subtitle_y = int(13 * 11.81)  # 13mm from top

        draw.text((subtitle_x, subtitle_y), date_text, fill='#adb5bd', font=subtitle_font)

    def _add_participant_photo_template(self, draw, width, height):
        """Add participant photo area like SVG template (40x40mm)"""
        # Photo dimensions (40x40mm)
        photo_size = int(40 * 11.81)  # ~472 pixels
        photo_x = (width - photo_size) // 2
        photo_y = int(25 * 11.81)  # 25mm from top

        # Photo background
        draw.rounded_rectangle([photo_x, photo_y, photo_x + photo_size, photo_y + photo_size],
                             radius=int(2 * 11.81), fill='#e9ecef')

        # Try to load participant photo
        if self.participant.profile_photo:
            try:
                photo_img = Image.open(self.participant.profile_photo.path)
                photo_img = photo_img.convert('RGB')
                photo_img = photo_img.resize((photo_size, photo_size), Image.Resampling.LANCZOS)

                # Create rounded corners mask
                mask = Image.new('L', (photo_size, photo_size), 0)
                mask_draw = ImageDraw.Draw(mask)
                mask_draw.rounded_rectangle([0, 0, photo_size, photo_size],
                                          radius=int(2 * 11.81), fill=255)

                # Apply mask and paste
                output = Image.new('RGB', (photo_size, photo_size), '#e9ecef')
                output.paste(photo_img, (0, 0))
                output.putalpha(mask)

                badge_img = draw._image
                badge_img.paste(output, (photo_x, photo_y), output)

            except Exception:
                # Fallback: show "PHOTO" text
                self._draw_photo_placeholder_template(draw, photo_x, photo_y, photo_size)
        else:
            self._draw_photo_placeholder_template(draw, photo_x, photo_y, photo_size)

    def _draw_photo_placeholder_template(self, draw, x, y, size):
        """Draw photo placeholder like SVG template"""
        try:
            placeholder_font = ImageFont.truetype("arial.ttf", 24)
        except:
            placeholder_font = ImageFont.load_default()

        placeholder_text = "PHOTO"
        text_bbox = draw.textbbox((0, 0), placeholder_text, font=placeholder_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        text_x = x + (size - text_width) // 2
        text_y = y + (size - text_height) // 2

        draw.text((text_x, text_y), placeholder_text, fill='#6c757d', font=placeholder_font)

    def _add_participant_info_template(self, draw, width, height):
        """Add participant information like SVG template"""
        try:
            name_font = ImageFont.truetype("arial.ttf", 54)  # Large name
        except:
            try:
                name_font = ImageFont.truetype("DejaVuSans-Bold.ttf", 54)
            except:
                name_font = ImageFont.load_default()

        # Participant name (75mm from top)
        full_name = f"{self.participant.first_name} {self.participant.last_name}".upper()
        if len(full_name) > 20:
            full_name = full_name[:17] + "..."

        name_y = int(75 * 11.81)  # 75mm from top
        name_bbox = draw.textbbox((0, 0), full_name, font=name_font)
        name_width = name_bbox[2] - name_bbox[0]
        name_x = (width - name_width) // 2

        draw.text((name_x, name_y), full_name, fill='#212529', font=name_font)

    def _add_participant_type_template(self, draw, width, height):
        """Add participant type badge like SVG template"""
        try:
            type_font = ImageFont.truetype("arial.ttf", 42)  # Bold type text
        except:
            try:
                type_font = ImageFont.truetype("DejaVuSans-Bold.ttf", 42)
            except:
                type_font = ImageFont.load_default()

        # Get participant type
        ptype = self.participant.participant_type.name.upper() if self.participant.participant_type else "PARTICIPANT"

        # Type badge dimensions (30mm width, 8mm height)
        badge_width = int(30 * 11.81)  # ~354 pixels
        badge_height = int(8 * 11.81)   # ~95 pixels
        badge_x = (width - badge_width) // 2
        badge_y = int(80 * 11.81)  # 80mm from top

        # Create gradient background (red gradient like SVG)
        for y in range(badge_height):
            progress = y / badge_height
            # Red gradient from #e63946 to #c1121f
            r = int(230 + (193 - 230) * progress)
            g = int(57 + (18 - 57) * progress)
            b = int(70 + (31 - 70) * progress)
            draw.line([(badge_x, badge_y + y), (badge_x + badge_width, badge_y + y)], fill=(r, g, b))

        # Round the corners
        draw.rounded_rectangle([badge_x, badge_y, badge_x + badge_width, badge_y + badge_height],
                             radius=int(4 * 11.81), fill=None, outline=None)

        # Badge text
        text_bbox = draw.textbbox((0, 0), ptype, font=type_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        text_x = badge_x + (badge_width - text_width) // 2
        text_y = badge_y + (badge_height - text_height) // 2

        draw.text((text_x, text_y), ptype, fill='white', font=type_font)

    def _add_organization_template(self, draw, width, height):
        """Add organization info like SVG template"""
        try:
            org_font = ImageFont.truetype("arial.ttf", 38)      # Organization name
            position_font = ImageFont.truetype("arial.ttf", 33) # Position
        except:
            try:
                org_font = ImageFont.truetype("DejaVuSans.ttf", 38)
                position_font = ImageFont.truetype("DejaVuSans.ttf", 33)
            except:
                org_font = ImageFont.load_default()
                position_font = ImageFont.load_default()

        # Organization name (100mm from top)
        institution = getattr(self.participant, 'institution_name', '') or "UNIVERSITY OF GONDAR"
        institution = institution.upper()
        if len(institution) > 25:
            institution = institution[:22] + "..."

        org_y = int(100 * 11.81)  # 100mm from top
        org_bbox = draw.textbbox((0, 0), institution, font=org_font)
        org_width = org_bbox[2] - org_bbox[0]
        org_x = (width - org_width) // 2

        draw.text((org_x, org_y), institution, fill='#495057', font=org_font)

        # Position (104mm from top)
        position = getattr(self.participant, 'position', '') or "Participant"
        if len(position) > 30:
            position = position[:27] + "..."

        position_y = int(104 * 11.81)  # 104mm from top
        position_bbox = draw.textbbox((0, 0), position, font=position_font)
        position_width = position_bbox[2] - position_bbox[0]
        position_x = (width - position_width) // 2

        draw.text((position_x, position_y), position, fill='#6c757d', font=position_font)

    def _add_qr_code_template(self, draw, width, height, qr_img):
        """Add QR code like SVG template (25x25mm)"""
        # QR code dimensions (25x25mm)
        qr_size = int(25 * 11.81)  # ~295 pixels
        qr_x = (width - qr_size) // 2
        qr_y = int(115 * 11.81)  # 115mm from top

        # QR code background
        draw.rectangle([qr_x, qr_y, qr_x + qr_size, qr_y + qr_size],
                      fill='white', outline='#dee2e6', width=4)

        # Resize and paste QR code
        qr_resized = qr_img.resize((qr_size - 20, qr_size - 20), Image.Resampling.LANCZOS)
        badge_img = draw._image
        badge_img.paste(qr_resized, (qr_x + 10, qr_y + 10))

        # QR code label
        try:
            qr_font = ImageFont.truetype("arial.ttf", 21)
        except:
            qr_font = ImageFont.load_default()

        qr_label = "Scan for contact info"
        label_y = int(145 * 11.81)  # 145mm from top
        label_bbox = draw.textbbox((0, 0), qr_label, font=qr_font)
        label_width = label_bbox[2] - label_bbox[0]
        label_x = (width - label_width) // 2

        draw.text((label_x, label_y), qr_label, fill='#495057', font=qr_font)

    def _add_sponsors_template(self, draw, width, height):
        """Add sponsors section like SVG template"""
        try:
            from events.models import EventSponsor
            sponsors = EventSponsor.objects.filter(event=self.participant.event, is_active=True)[:3]
        except:
            sponsors = []

        # Sponsors section (155mm from top)
        section_y = int(155 * 11.81)  # 155mm from top

        # Separator line
        line_x = int(5 * 11.81)   # 5mm from left
        line_width = int(42.5 * 11.81)  # 42.5mm width
        draw.rectangle([line_x, section_y, line_x + line_width, section_y + 6], fill='#adb5bd')

        # "EVENT PARTNERS" label
        try:
            label_font = ImageFont.truetype("arial.ttf", 21)
        except:
            label_font = ImageFont.load_default()

        label_text = "EVENT PARTNERS"
        label_y = int(160 * 11.81)  # 160mm from top
        label_bbox = draw.textbbox((0, 0), label_text, font=label_font)
        label_width = label_bbox[2] - label_bbox[0]
        label_x = (width - label_width) // 2

        draw.text((label_x, label_y), label_text, fill='#6c757d', font=label_font)

        # Sponsor logos (3 columns, 12x12mm each)
        logo_size = int(12 * 11.81)  # ~142 pixels
        logo_y = int(165 * 11.81)    # 165mm from top

        # Calculate positions for 3 logos
        total_width = 3 * logo_size + 2 * int(0.25 * 11.81)  # logos + spacing
        start_x = (width - total_width) // 2

        for i, sponsor in enumerate(sponsors):
            logo_x = start_x + i * (logo_size + int(0.25 * 11.81))

            # Logo background
            draw.rounded_rectangle([logo_x, logo_y, logo_x + logo_size, logo_y + logo_size],
                                 radius=int(1 * 11.81), fill='#e9ecef')

            # Try to load sponsor logo
            if sponsor.logo:
                try:
                    logo_img = Image.open(sponsor.logo.path)
                    logo_img = logo_img.convert('RGBA')
                    logo_img.thumbnail((logo_size - 20, logo_size - 20), Image.Resampling.LANCZOS)

                    # Center the logo
                    logo_paste_x = logo_x + (logo_size - logo_img.width) // 2
                    logo_paste_y = logo_y + (logo_size - logo_img.height) // 2

                    badge_img = draw._image
                    badge_img.paste(logo_img, (logo_paste_x, logo_paste_y), logo_img)

                except Exception:
                    # Fallback: draw sponsor name
                    self._draw_sponsor_name_template(draw, sponsor, logo_x, logo_y, logo_size)
            else:
                self._draw_sponsor_name_template(draw, sponsor, logo_x, logo_y, logo_size)

    def _draw_sponsor_name_template(self, draw, sponsor, x, y, size):
        """Draw sponsor name as fallback"""
        try:
            sponsor_font = ImageFont.truetype("arial.ttf", 16)
        except:
            sponsor_font = ImageFont.load_default()

        # Truncate name if too long
        name = sponsor.name
        if len(name) > 8:
            name = name[:6] + ".."

        # Center text
        text_bbox = draw.textbbox((0, 0), name, font=sponsor_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        text_x = x + (size - text_width) // 2
        text_y = y + (size - text_height) // 2

        draw.text((text_x, text_y), name, fill='#495057', font=sponsor_font)

    def _add_footer_template(self, draw, width, height):
        """Add footer like SVG template"""
        # Footer background (17mm height at bottom)
        footer_height = int(17 * 11.81)  # ~200 pixels
        footer_y = height - footer_height

        draw.rectangle([0, footer_y, width, height], fill='#e9ecef')

        # Footer text
        try:
            footer_font = ImageFont.truetype("arial.ttf", 19)
        except:
            footer_font = ImageFont.load_default()

        footer_text = "Organizer: University of Gondar"
        footer_text_y = footer_y + int(10 * 11.81)  # 10mm from footer top

        text_bbox = draw.textbbox((0, 0), footer_text, font=footer_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_x = (width - text_width) // 2

        draw.text((text_x, footer_text_y), footer_text, fill='#495057', font=footer_font)

    def _add_dark_blue_banner(self, draw, width, height):
        """Add dark blue pattern banner extending to half of photo area"""
        # Extended banner height to cover half of photo area
        photo_y = 200  # Photo position
        photo_size = 200  # Photo size
        banner_height = photo_y + (photo_size // 2)  # Extend to half of photo

        # Create gradient from dark blue to lighter blue
        for y in range(banner_height):
            # Enhanced dark blue gradient (15, 35, 75) to (35, 65, 125)
            progress = y / banner_height
            r = int(15 + (20 * progress))
            g = int(35 + (30 * progress))
            b = int(75 + (50 * progress))
            draw.line([(0, y), (width, y)], fill=(r, g, b))

        # Add high-quality subtle geometric pattern
        pattern_color = (255, 255, 255, 20)  # More subtle semi-transparent white
        for x in range(0, width, 80):  # Increased spacing for better quality
            for y in range(0, banner_height, 40):
                # Draw refined small diamonds
                diamond_size = 12  # Larger for better visibility at high resolution
                points = [
                    (x + diamond_size, y),
                    (x + diamond_size * 2, y + diamond_size),
                    (x + diamond_size, y + diamond_size * 2),
                    (x, y + diamond_size)
                ]
                # Create temporary image for alpha blending
                temp_img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
                temp_draw = ImageDraw.Draw(temp_img)
                temp_draw.polygon(points, fill=pattern_color)
                badge_img = draw._image
                badge_img.paste(temp_img, (0, 0), temp_img)

    def _add_event_title_and_motto(self, draw, width, height):
        """Add event title and motto in the banner area with high quality"""
        try:
            # Load high-quality fonts with larger sizes for better resolution
            title_font = ImageFont.truetype("arial.ttf", 42)  # Increased for high resolution
            motto_font = ImageFont.truetype("arial.ttf", 24)  # Increased for high resolution
        except:
            try:
                title_font = ImageFont.truetype("DejaVuSans-Bold.ttf", 42)
                motto_font = ImageFont.truetype("DejaVuSans.ttf", 24)
            except:
                title_font = ImageFont.load_default()
                motto_font = ImageFont.load_default()

        # Event title
        event_name = self.participant.event.name
        if len(event_name) > 35:
            event_name = event_name[:32] + "..."

        # Center the title
        title_bbox = draw.textbbox((0, 0), event_name, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (width - title_width) // 2

        # Add text shadow
        draw.text((title_x + 2, 22), event_name, fill=(0, 0, 0, 100), font=title_font)
        draw.text((title_x, 20), event_name, fill='white', font=title_font)

        # Event motto/description (if available)
        motto = getattr(self.participant.event, 'motto', '') or "Excellence in Education"
        if len(motto) > 50:
            motto = motto[:47] + "..."

        motto_bbox = draw.textbbox((0, 0), motto, font=motto_font)
        motto_width = motto_bbox[2] - motto_bbox[0]
        motto_x = (width - motto_width) // 2

        # Add motto with shadow
        draw.text((motto_x + 1, 56), motto, fill=(0, 0, 0, 80), font=motto_font)
        draw.text((motto_x, 55), motto, fill='white', font=motto_font)

    def _add_participant_photo_enhanced(self, draw, width, height):
        """Add participant photo with enhanced circular frame and high quality"""
        photo_size = 200  # Increased size for better quality
        photo_x = (width - photo_size) // 2
        photo_y = 200  # Positioned to intersect with banner

        # Create circular mask
        mask = Image.new('L', (photo_size, photo_size), 0)
        mask_draw = ImageDraw.Draw(mask)
        mask_draw.ellipse([0, 0, photo_size, photo_size], fill=255)

        # Load and process participant photo
        if self.participant.profile_photo:
            try:
                photo_img = Image.open(self.participant.profile_photo.path)
                photo_img = photo_img.convert('RGB')
                photo_img = photo_img.resize((photo_size, photo_size), Image.Resampling.LANCZOS)

                # Apply circular mask
                output = Image.new('RGB', (photo_size, photo_size), (255, 255, 255))
                output.paste(photo_img, (0, 0))
                output.putalpha(mask)

                # Add decorative frame
                frame_size = photo_size + 12
                frame_x = photo_x - 6
                frame_y = photo_y - 6

                # Outer frame (gold)
                draw.ellipse([frame_x, frame_y, frame_x + frame_size, frame_y + frame_size],
                           fill=(218, 165, 32), outline=(184, 134, 11), width=3)

                # Inner frame (dark blue)
                inner_frame_size = photo_size + 8
                inner_frame_x = photo_x - 4
                inner_frame_y = photo_y - 4
                draw.ellipse([inner_frame_x, inner_frame_y, inner_frame_x + inner_frame_size, inner_frame_y + inner_frame_size],
                           fill=(25, 45, 85), outline=(15, 25, 55), width=2)

                # Paste the photo
                badge_img = draw._image
                badge_img.paste(output, (photo_x, photo_y), output)

            except Exception as e:
                # Fallback: draw placeholder with icon
                self._draw_photo_placeholder_enhanced(draw, photo_x, photo_y, photo_size)
        else:
            self._draw_photo_placeholder_enhanced(draw, photo_x, photo_y, photo_size)

    def _draw_photo_placeholder_enhanced(self, draw, x, y, size):
        """Draw enhanced photo placeholder"""
        # Outer frame (gold)
        frame_size = size + 12
        frame_x = x - 6
        frame_y = y - 6
        draw.ellipse([frame_x, frame_y, frame_x + frame_size, frame_y + frame_size],
                   fill=(218, 165, 32), outline=(184, 134, 11), width=3)

        # Inner frame (dark blue)
        inner_frame_size = size + 8
        inner_frame_x = x - 4
        inner_frame_y = y - 4
        draw.ellipse([inner_frame_x, inner_frame_y, inner_frame_x + inner_frame_size, inner_frame_y + inner_frame_size],
                   fill=(25, 45, 85), outline=(15, 25, 55), width=2)

        # Photo area
        draw.ellipse([x, y, x + size, y + size], fill=(240, 240, 240), outline=(200, 200, 200), width=2)

        # User icon
        try:
            icon_font = ImageFont.truetype("arial.ttf", 60)
        except:
            icon_font = ImageFont.load_default()

        icon_text = "👤"
        icon_bbox = draw.textbbox((0, 0), icon_text, font=icon_font)
        icon_width = icon_bbox[2] - icon_bbox[0]
        icon_height = icon_bbox[3] - icon_bbox[1]
        draw.text((x + size//2 - icon_width//2, y + size//2 - icon_height//2),
                 icon_text, fill=(25, 45, 85), font=icon_font)

    def _add_participant_info_enhanced(self, draw, width, height):
        """Add participant information with enhanced visibility and high quality"""
        try:
            name_font = ImageFont.truetype("arial.ttf", 48)  # Increased for high resolution
            info_font = ImageFont.truetype("arial.ttf", 30)  # Increased for high resolution
            small_font = ImageFont.truetype("arial.ttf", 24)  # Increased for high resolution
        except:
            try:
                name_font = ImageFont.truetype("DejaVuSans-Bold.ttf", 48)
                info_font = ImageFont.truetype("DejaVuSans.ttf", 30)
                small_font = ImageFont.truetype("DejaVuSans.ttf", 24)
            except:
                name_font = ImageFont.load_default()
                info_font = ImageFont.load_default()
                small_font = ImageFont.load_default()

        # Starting position after photo
        start_y = 420  # Adjusted for larger photo

        # Participant name with enhanced styling
        full_name = f"{self.participant.first_name} {self.participant.last_name}"
        if len(full_name) > 25:
            full_name = full_name[:22] + "..."

        # Name background for better visibility
        name_bbox = draw.textbbox((0, 0), full_name, font=name_font)
        name_width = name_bbox[2] - name_bbox[0]
        name_height = name_bbox[3] - name_bbox[1]
        name_x = (width - name_width) // 2

        # Add subtle background for name
        bg_padding = 15
        draw.rounded_rectangle([name_x - bg_padding, start_y - 5,
                              name_x + name_width + bg_padding, start_y + name_height + 5],
                             radius=8, fill=(248, 250, 252), outline=(226, 232, 240), width=1)

        # Add name with shadow
        draw.text((name_x + 2, start_y + 2), full_name, fill=(0, 0, 0, 60), font=name_font)
        draw.text((name_x, start_y), full_name, fill=(25, 45, 85), font=name_font)

        # Position/Title
        position = getattr(self.participant, 'position', '') or getattr(self.participant, 'title', '') or "Participant"
        if len(position) > 30:
            position = position[:27] + "..."

        position_y = start_y + 50
        position_bbox = draw.textbbox((0, 0), position, font=info_font)
        position_width = position_bbox[2] - position_bbox[0]
        position_x = (width - position_width) // 2

        draw.text((position_x + 1, position_y + 1), position, fill=(0, 0, 0, 40), font=info_font)
        draw.text((position_x, position_y), position, fill=(75, 85, 99), font=info_font)

    def _add_participant_type_prominent(self, draw, width, height):
        """Add prominent participant type badge with high quality"""
        try:
            type_font = ImageFont.truetype("arial.ttf", 36)  # Increased for high resolution
        except:
            try:
                type_font = ImageFont.truetype("DejaVuSans-Bold.ttf", 36)
            except:
                type_font = ImageFont.load_default()

        # Get participant type
        ptype = self.participant.participant_type.name.upper() if self.participant.participant_type else "PARTICIPANT"

        # Position for type badge
        type_y = 540  # Adjusted for larger elements

        # Badge dimensions
        badge_padding = 20
        type_bbox = draw.textbbox((0, 0), ptype, font=type_font)
        badge_width = (type_bbox[2] - type_bbox[0]) + (badge_padding * 2)
        badge_height = (type_bbox[3] - type_bbox[1]) + 16
        badge_x = (width - badge_width) // 2

        # Badge colors based on type with enhanced visibility
        if 'VIP' in ptype or 'SPEAKER' in ptype or 'KEYNOTE' in ptype:
            badge_color = (220, 38, 127)  # Vibrant pink
            text_color = 'white'
            border_color = (190, 24, 93)
        elif 'STAFF' in ptype or 'ORGANIZER' in ptype:
            badge_color = (16, 185, 129)  # Emerald green
            text_color = 'white'
            border_color = (5, 150, 105)
        elif 'STUDENT' in ptype:
            badge_color = (245, 158, 11)  # Amber
            text_color = 'white'
            border_color = (217, 119, 6)
        elif 'FACULTY' in ptype or 'PROFESSOR' in ptype:
            badge_color = (99, 102, 241)  # Indigo
            text_color = 'white'
            border_color = (79, 70, 229)
        else:
            badge_color = (59, 130, 246)  # Blue
            text_color = 'white'
            border_color = (37, 99, 235)

        # Add shadow
        shadow_offset = 3
        draw.rounded_rectangle([badge_x + shadow_offset, type_y + shadow_offset,
                              badge_x + badge_width + shadow_offset, type_y + badge_height + shadow_offset],
                             radius=12, fill=(0, 0, 0, 30))

        # Draw main badge
        draw.rounded_rectangle([badge_x, type_y, badge_x + badge_width, type_y + badge_height],
                             radius=12, fill=badge_color, outline=border_color, width=3)

        # Add gradient effect
        for i in range(badge_height // 3):
            alpha = int(50 * (1 - i / (badge_height // 3)))
            gradient_color = (*badge_color, alpha)
            temp_img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            temp_draw = ImageDraw.Draw(temp_img)
            temp_draw.rounded_rectangle([badge_x, type_y + i, badge_x + badge_width, type_y + i + 1],
                                      radius=12, fill=gradient_color)
            badge_img = draw._image
            badge_img.paste(temp_img, (0, 0), temp_img)

        # Badge text
        text_x = badge_x + badge_padding
        text_y = type_y + 8

        # Add text shadow
        draw.text((text_x + 1, text_y + 1), ptype, fill=(0, 0, 0, 80), font=type_font)
        draw.text((text_x, text_y), ptype, fill=text_color, font=type_font)

    def _add_qr_code_enhanced(self, draw, width, height, qr_img):
        """Add QR code with enhanced professional frame and high quality"""
        qr_size = 180  # Increased size for better scanning
        qr_x = width - qr_size - 45  # Adjusted margin
        qr_y = 650  # Adjusted position

        # QR code frame
        frame_padding = 8
        frame_x = qr_x - frame_padding
        frame_y = qr_y - frame_padding
        frame_size = qr_size + (frame_padding * 2)

        # Add shadow
        shadow_offset = 4
        draw.rounded_rectangle([frame_x + shadow_offset, frame_y + shadow_offset,
                              frame_x + frame_size + shadow_offset, frame_y + frame_size + shadow_offset],
                             radius=8, fill=(0, 0, 0, 40))

        # Frame background
        draw.rounded_rectangle([frame_x, frame_y, frame_x + frame_size, frame_y + frame_size],
                             radius=8, fill='white', outline=(25, 45, 85), width=3)

        # Resize and paste QR code
        qr_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
        badge_img = draw._image
        badge_img.paste(qr_resized, (qr_x, qr_y))

        # QR code label
        try:
            qr_font = ImageFont.truetype("arial.ttf", 14)
        except:
            qr_font = ImageFont.load_default()

        qr_label = "SCAN ME"
        label_bbox = draw.textbbox((0, 0), qr_label, font=qr_font)
        label_width = label_bbox[2] - label_bbox[0]
        label_x = qr_x + (qr_size - label_width) // 2
        label_y = qr_y + qr_size + 10

        draw.text((label_x + 1, label_y + 1), qr_label, fill=(0, 0, 0, 60), font=qr_font)
        draw.text((label_x, label_y), qr_label, fill=(25, 45, 85), font=qr_font)

    def _add_all_sponsor_logos(self, draw, width, height):
        """Add all sponsor logos in organized layout"""
        try:
            from events.models import EventSponsor
            sponsors = EventSponsor.objects.filter(event=self.participant.event, is_active=True).order_by('-sponsor_type', 'name')
        except:
            sponsors = []

        if not sponsors:
            return

        # Sponsor section positioning
        sponsor_start_y = 900  # Adjusted for larger elements
        logo_size = 90  # Increased for better visibility
        spacing = 20  # Increased spacing

        # Add sponsor section header
        try:
            header_font = ImageFont.truetype("arial.ttf", 18)
        except:
            header_font = ImageFont.load_default()

        header_text = "SPONSORS"
        header_bbox = draw.textbbox((0, 0), header_text, font=header_font)
        header_width = header_bbox[2] - header_bbox[0]
        header_x = (width - header_width) // 2

        # Header background
        draw.rounded_rectangle([header_x - 20, sponsor_start_y - 5,
                              header_x + header_width + 20, sponsor_start_y + 25],
                             radius=6, fill=(248, 250, 252), outline=(203, 213, 225), width=1)

        draw.text((header_x, sponsor_start_y), header_text, fill=(25, 45, 85), font=header_font)

        # Calculate layout
        logos_per_row = min(4, len(sponsors))
        if len(sponsors) <= 4:
            rows = 1
        elif len(sponsors) <= 8:
            rows = 2
        else:
            rows = 3
            logos_per_row = 3

        # Position sponsors
        current_row = 0
        current_col = 0
        logo_y = sponsor_start_y + 40

        for i, sponsor in enumerate(sponsors[:9]):  # Limit to 9 sponsors
            # Calculate position
            row_width = logos_per_row * (logo_size + spacing) - spacing
            start_x = (width - row_width) // 2
            logo_x = start_x + current_col * (logo_size + spacing)

            # Draw sponsor logo
            self._draw_sponsor_logo(draw, sponsor, logo_x, logo_y, logo_size)

            current_col += 1
            if current_col >= logos_per_row:
                current_col = 0
                current_row += 1
                logo_y += logo_size + spacing + 10

                # Adjust for fewer logos in last row
                if current_row == rows - 1:
                    remaining_sponsors = len(sponsors) - (current_row * logos_per_row)
                    if remaining_sponsors < logos_per_row:
                        logos_per_row = remaining_sponsors

    def _draw_sponsor_logo(self, draw, sponsor, x, y, size):
        """Draw individual sponsor logo with type indicator"""
        # Logo frame with sponsor type color
        type_colors = {
            'PLATINUM': (229, 231, 235),  # Platinum
            'GOLD': (255, 215, 0),        # Gold
            'SILVER': (192, 192, 192),    # Silver
            'BRONZE': (205, 127, 50),     # Bronze
        }

        frame_color = type_colors.get(sponsor.sponsor_type, (156, 163, 175))

        # Add shadow
        shadow_offset = 2
        draw.rounded_rectangle([x + shadow_offset, y + shadow_offset,
                              x + size + shadow_offset, y + size + shadow_offset],
                             radius=6, fill=(0, 0, 0, 30))

        # Frame
        draw.rounded_rectangle([x, y, x + size, y + size],
                             radius=6, fill='white', outline=frame_color, width=3)

        # Try to load sponsor logo
        if sponsor.logo:
            try:
                logo_img = Image.open(sponsor.logo.path)
                logo_img = logo_img.convert('RGBA')

                # Resize maintaining aspect ratio
                logo_img.thumbnail((size - 10, size - 10), Image.Resampling.LANCZOS)

                # Center the logo
                logo_x = x + (size - logo_img.width) // 2
                logo_y = y + (size - logo_img.height) // 2

                badge_img = draw._image
                badge_img.paste(logo_img, (logo_x, logo_y), logo_img)

            except Exception:
                # Fallback: draw sponsor name
                self._draw_sponsor_name_fallback(draw, sponsor, x, y, size)
        else:
            self._draw_sponsor_name_fallback(draw, sponsor, x, y, size)

        # Add type indicator
        type_indicator_size = 16
        indicator_x = x + size - type_indicator_size - 2
        indicator_y = y + 2

        draw.ellipse([indicator_x, indicator_y,
                     indicator_x + type_indicator_size, indicator_y + type_indicator_size],
                    fill=frame_color, outline='white', width=2)

    def _draw_sponsor_name_fallback(self, draw, sponsor, x, y, size):
        """Draw sponsor name as fallback when logo is not available"""
        try:
            sponsor_font = ImageFont.truetype("arial.ttf", 12)
        except:
            sponsor_font = ImageFont.load_default()

        # Truncate name if too long
        name = sponsor.name
        if len(name) > 8:
            name = name[:6] + ".."

        # Center text
        text_bbox = draw.textbbox((0, 0), name, font=sponsor_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        text_x = x + (size - text_width) // 2
        text_y = y + (size - text_height) // 2

        draw.text((text_x, text_y), name, fill=(25, 45, 85), font=sponsor_font)

    def _add_institution_and_dates(self, draw, width, height):
        """Add institution and event dates with high quality"""
        try:
            info_font = ImageFont.truetype("arial.ttf", 24)  # Increased for high resolution
            small_font = ImageFont.truetype("arial.ttf", 20)  # Increased for high resolution
        except:
            try:
                info_font = ImageFont.truetype("DejaVuSans.ttf", 24)
                small_font = ImageFont.truetype("DejaVuSans.ttf", 20)
            except:
                info_font = ImageFont.load_default()
                small_font = ImageFont.load_default()

        # Institution
        institution = getattr(self.participant, 'institution_name', '') or "University of Gondar"
        if len(institution) > 35:
            institution = institution[:32] + "..."

        institution_y = height - 120  # Adjusted for larger elements
        institution_bbox = draw.textbbox((0, 0), institution, font=info_font)
        institution_width = institution_bbox[2] - institution_bbox[0]
        institution_x = 30

        # Institution background
        draw.rounded_rectangle([institution_x - 10, institution_y - 5,
                              institution_x + institution_width + 10, institution_y + 25],
                             radius=6, fill=(248, 250, 252), outline=(203, 213, 225), width=1)

        draw.text((institution_x, institution_y), institution, fill=(25, 45, 85), font=info_font)

        # Event dates
        event = self.participant.event
        if event.start_date and event.end_date:
            if event.start_date == event.end_date:
                date_text = event.start_date.strftime("%B %d, %Y")
            else:
                date_text = f"{event.start_date.strftime('%b %d')} - {event.end_date.strftime('%b %d, %Y')}"
        else:
            date_text = "Event Date TBA"

        date_y = height - 45
        date_bbox = draw.textbbox((0, 0), date_text, font=small_font)
        date_width = date_bbox[2] - date_bbox[0]
        date_x = width - date_width - 30

        # Date background
        draw.rounded_rectangle([date_x - 10, date_y - 5,
                              date_x + date_width + 10, date_y + 20],
                             radius=6, fill=(248, 250, 252), outline=(203, 213, 225), width=1)

        draw.text((date_x, date_y), date_text, fill=(75, 85, 99), font=small_font)

    def _add_subtle_background_pattern(self, draw, width, height):
        """Add subtle background pattern for professional look"""
        import math

        # Very subtle diagonal lines
        for i in range(0, width + height, 100):
            x1, y1 = i, 0
            x2, y2 = 0, i
            if x1 <= width and y2 <= height:
                draw.line([(x1, y1), (x2, y2)], fill=(250, 250, 250), width=1)

        # Subtle dots pattern
        for x in range(50, width, 80):
            for y in range(50, height, 80):
                if (x + y) % 160 == 0:
                    draw.ellipse([x-1, y-1, x+1, y+1], fill=(248, 248, 248))

    def _create_event_header_new(self, draw, width, height):
        """Create event header like the sample"""
        # Get event information
        event = self.participant.event
        event_name = event.name

        # Header area
        header_height = 80

        # Event name at top (centered, professional font)
        try:
            header_font = ImageFont.load_default()
            large_font = ImageFont.load_default()
        except:
            header_font = ImageFont.load_default()
            large_font = ImageFont.load_default()

        # Split long event names into multiple lines
        max_chars = 35
        if len(event_name) > max_chars:
            words = event_name.split()
            lines = []
            current_line = ""

            for word in words:
                if len(current_line + word) <= max_chars:
                    current_line += word + " "
                else:
                    if current_line:
                        lines.append(current_line.strip())
                    current_line = word + " "
            if current_line:
                lines.append(current_line.strip())
        else:
            lines = [event_name]

        # Draw event name lines
        start_y = 20
        line_height = 25

        for i, line in enumerate(lines[:2]):  # Max 2 lines
            line_bbox = draw.textbbox((0, 0), line, font=header_font)
            line_width = line_bbox[2] - line_bbox[0]
            x_pos = width//2 - line_width//2
            y_pos = start_y + i * line_height

            # Add text shadow for better visibility
            draw.text((x_pos + 1, y_pos + 1), line, fill=(200, 200, 200), font=header_font)
            draw.text((x_pos, y_pos), line, fill=(184, 134, 11), font=header_font)  # Gold color

    def _add_participant_photo_new(self, draw, width, height):
        """Add participant photo with blue circular frame like sample"""
        photo_size = 120
        photo_x = width // 2 - photo_size // 2
        photo_y = 90  # Position to intersect with header

        # Try to get participant photo
        participant_photo = None
        if self.participant.profile_photo:
            try:
                participant_photo = Image.open(self.participant.profile_photo.path)
            except:
                participant_photo = None

        if participant_photo:
            # Resize and make circular
            participant_photo = participant_photo.resize((photo_size, photo_size), Image.Resampling.LANCZOS)

            # Create circular mask
            mask = Image.new('L', (photo_size, photo_size), 0)
            mask_draw = ImageDraw.Draw(mask)
            mask_draw.ellipse([0, 0, photo_size, photo_size], fill=255)

            # Apply mask to photo
            participant_photo.putalpha(mask)

            # Create blue circular frame (like sample)
            frame_width = 4
            frame_color = (59, 130, 246)  # Blue color like sample

            # Draw multiple circles for frame effect
            for i in range(frame_width):
                draw.ellipse([
                    photo_x - frame_width + i, photo_y - frame_width + i,
                    photo_x + photo_size + frame_width - i, photo_y + photo_size + frame_width - i
                ], outline=frame_color, width=1)

            # Paste the photo
            badge_img = draw._image
            badge_img.paste(participant_photo, (photo_x, photo_y), participant_photo)
        else:
            # Create placeholder with blue frame
            frame_color = (59, 130, 246)
            draw.ellipse([photo_x-4, photo_y-4, photo_x + photo_size + 4, photo_y + photo_size + 4],
                        outline=frame_color, width=4)
            draw.ellipse([photo_x, photo_y, photo_x + photo_size, photo_y + photo_size],
                        fill=(240, 248, 255), outline=frame_color, width=2)

            # Add placeholder icon
            icon_text = "👤"
            try:
                icon_font = ImageFont.load_default()
            except:
                icon_font = ImageFont.load_default()

            icon_bbox = draw.textbbox((0, 0), icon_text, font=icon_font)
            icon_width = icon_bbox[2] - icon_bbox[0]
            icon_height = icon_bbox[3] - icon_bbox[1]
            draw.text((photo_x + photo_size//2 - icon_width//2, photo_y + photo_size//2 - icon_height//2),
                     icon_text, fill=frame_color, font=icon_font)

    def _add_participant_info_professional(self, draw, width, height):
        """Add participant information like the sample"""
        # Participant name (large, bold, centered)
        name = self.participant.full_name
        name_y = 230

        try:
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()
        except:
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()

        # Draw name with shadow for better visibility
        name_bbox = draw.textbbox((0, 0), name, font=name_font)
        name_width = name_bbox[2] - name_bbox[0]
        x_pos = width//2 - name_width//2

        # Text shadow
        draw.text((x_pos + 1, name_y + 1), name, fill=(200, 200, 200), font=name_font)
        # Main text
        draw.text((x_pos, name_y), name, fill=(31, 41, 55), font=name_font)  # Dark gray

        # Institution name
        institution = self.participant.institution_name or 'Unknown Institution'
        if len(institution) > 40:
            institution = institution[:37] + "..."

        inst_y = name_y + 35
        inst_bbox = draw.textbbox((0, 0), institution, font=info_font)
        inst_width = inst_bbox[2] - inst_bbox[0]
        draw.text((width//2 - inst_width//2, inst_y), institution, fill=(107, 114, 128), font=info_font)

        # Participant type badge (like "VIP" in sample)
        ptype = self.participant.participant_type.name.upper()
        badge_y = inst_y + 40

        # Create colored badge
        badge_width = len(ptype) * 12 + 20
        badge_height = 25
        badge_x = width//2 - badge_width//2

        # Badge colors based on type
        if 'VIP' in ptype or 'SPEAKER' in ptype:
            badge_color = (59, 130, 246)  # Blue like sample
        elif 'STAFF' in ptype or 'ORGANIZER' in ptype:
            badge_color = (16, 185, 129)  # Green
        elif 'STUDENT' in ptype:
            badge_color = (245, 158, 11)  # Yellow
        else:
            badge_color = (59, 130, 246)  # Default blue

        # Draw rounded rectangle badge
        draw.rectangle([badge_x, badge_y, badge_x + badge_width, badge_y + badge_height],
                      fill=badge_color)

        # Badge text
        badge_bbox = draw.textbbox((0, 0), ptype, font=info_font)
        badge_text_width = badge_bbox[2] - badge_bbox[0]
        draw.text((width//2 - badge_text_width//2, badge_y + 5), ptype, fill='white', font=info_font)

    def _add_qr_code_professional(self, draw, width, height, qr_img):
        """Add QR code with professional frame like sample"""
        qr_size = 120
        qr_x = width // 2 - qr_size // 2
        qr_y = height - 280  # Position above sponsors

        # Resize QR code
        qr_img_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)

        # Create frame
        frame_padding = 8
        frame_color = (31, 41, 55)  # Dark gray

        # Draw frame background
        draw.rectangle([
            qr_x - frame_padding, qr_y - frame_padding,
            qr_x + qr_size + frame_padding, qr_y + qr_size + frame_padding
        ], fill='white', outline=frame_color, width=2)

        # Paste QR code
        badge_img = draw._image
        badge_img.paste(qr_img_resized, (qr_x, qr_y))

    def _add_sponsors_footer(self, draw, width, height):
        """Add sponsors and organizers at bottom like sample"""
        try:
            from events.models import EventSponsor, EventOrganizer

            # Get sponsors and organizers
            sponsors = EventSponsor.objects.filter(
                event=self.participant.event,
                is_active=True
            ).order_by('display_order')[:3]

            organizers = EventOrganizer.objects.filter(
                event=self.participant.event,
                is_active=True
            ).order_by('display_order')[:3]

            footer_y = height - 120
            logo_size = 50

            # Calculate total logos and spacing
            total_logos = len(sponsors) + len(organizers)
            if total_logos > 0:
                spacing = (width - 60) // total_logos
                start_x = 30

                current_x = start_x

                # Add sponsor logos
                for sponsor in sponsors:
                    if sponsor.logo:
                        try:
                            sponsor_img = Image.open(sponsor.logo.path)
                            sponsor_img = sponsor_img.resize((logo_size, logo_size), Image.Resampling.LANCZOS)

                            # Create circular mask for logos
                            mask = Image.new('L', (logo_size, logo_size), 0)
                            mask_draw = ImageDraw.Draw(mask)
                            mask_draw.ellipse([0, 0, logo_size, logo_size], fill=255)
                            sponsor_img.putalpha(mask)

                            # Paste logo
                            badge_img = draw._image
                            badge_img.paste(sponsor_img, (current_x, footer_y), sponsor_img)

                            current_x += spacing
                        except Exception as e:
                            print(f"Error loading sponsor logo: {e}")
                            current_x += spacing

                # Add organizer photos
                for organizer in organizers:
                    if organizer.photo:
                        try:
                            org_img = Image.open(organizer.photo.path)
                            org_img = org_img.resize((logo_size, logo_size), Image.Resampling.LANCZOS)

                            # Create circular mask
                            mask = Image.new('L', (logo_size, logo_size), 0)
                            mask_draw = ImageDraw.Draw(mask)
                            mask_draw.ellipse([0, 0, logo_size, logo_size], fill=255)
                            org_img.putalpha(mask)

                            # Paste photo
                            badge_img = draw._image
                            badge_img.paste(org_img, (current_x, footer_y), org_img)

                            current_x += spacing
                        except Exception as e:
                            print(f"Error loading organizer photo: {e}")
                            current_x += spacing

        except Exception as e:
            print(f"Error adding sponsors footer: {e}")

    def _add_event_dates(self, draw, width, height):
        """Add event dates at bottom right like sample"""
        event = self.participant.event

        # Format dates
        if hasattr(event, 'start_date') and event.start_date:
            if hasattr(event, 'end_date') and event.end_date and event.start_date.date() != event.end_date.date():
                date_text = f"{event.start_date.strftime('%B %d')} - {event.end_date.strftime('%d, %Y')}"
            else:
                date_text = event.start_date.strftime('%B %d, %Y')
        else:
            date_text = "Event Date TBD"

        try:
            date_font = ImageFont.load_default()
        except:
            date_font = ImageFont.load_default()

        # Position at bottom right
        date_bbox = draw.textbbox((0, 0), date_text, font=date_font)
        date_width = date_bbox[2] - date_bbox[0]

        x_pos = width - date_width - 20
        y_pos = height - 30

        # Add background for better visibility
        draw.rectangle([x_pos - 5, y_pos - 2, x_pos + date_width + 5, y_pos + 15],
                      fill=(184, 134, 11))  # Gold background

        draw.text((x_pos, y_pos), date_text, fill='white', font=date_font)


class DriverBadge(models.Model):
    driver = models.OneToOneField('drivers.Driver', on_delete=models.CASCADE, related_name='badge')
    template = models.ForeignKey(BadgeTemplate, on_delete=models.CASCADE, null=True, blank=True)

    # QR Code
    qr_code_data = models.TextField()  # JSON data for QR code
    qr_code_image = models.ImageField(upload_to='qr_codes/', null=True, blank=True)

    # Badge File
    badge_image = models.ImageField(upload_to='generated_badges/', null=True, blank=True)

    # Generation Status
    is_generated = models.BooleanField(default=False)
    generated_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Driver Badge for {self.driver.name}"

    def generate_qr_code(self):
        """Generate QR code for the driver"""
        import json
        from django.utils import timezone

        qr_data = {
            'driver_id': str(self.driver.id),
            'name': self.driver.name,
            'event': self.driver.event.name,
            'car_plate': self.driver.car_plate,
            'type': 'driver',
            'generated_at': timezone.now().isoformat()
        }

        self.qr_code_data = json.dumps(qr_data)

        # Generate QR code image
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(f"{settings.QR_CODE_BASE_URL}/verify/driver/{self.driver.id}")
        qr.make(fit=True)

        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Save QR code image
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        buffer.seek(0)

        filename = f"qr_driver_{self.driver.id}.png"
        self.qr_code_image.save(filename, File(buffer), save=False)

        return qr_img

    def generate_badge(self):
        """Generate the complete badge image for driver"""
        from django.utils import timezone

        # Get or create QR code
        if not self.qr_code_image:
            qr_img = self.generate_qr_code()
        else:
            qr_img = Image.open(self.qr_code_image.path)

        # Get template or use default dimensions
        template = self.template or BadgeTemplate.objects.filter(is_default=True).first()
        width = template.width if template else 450
        height = template.height if template else 650

        # Create badge image with gradient background
        badge_img = Image.new('RGB', (width, height), color='#ffffff')
        draw = ImageDraw.Draw(badge_img)

        # Add header banner with gradient (different color for drivers - green theme)
        banner_height = 80
        for y in range(banner_height):
            # Create gradient from green to dark green
            color_intensity = int(255 * (1 - y / banner_height))
            color = (30 + color_intensity//4, 120 + color_intensity//2, 60 + color_intensity//4)
            draw.line([(0, y), (width, y)], fill=color)

        # Load fonts
        try:
            title_font = ImageFont.load_default()
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        except:
            title_font = ImageFont.load_default()
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()
            small_font = ImageFont.load_default()

        # Add "DRIVER" title in header
        title_text = "DRIVER"
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        draw.text((width//2 - title_width//2, 25), title_text, fill='white', font=title_font)

        # Add driver photo placeholder or actual photo
        photo_size = 120
        photo_x = width//2 - photo_size//2
        photo_y = banner_height + 30

        if self.driver.photo:
            try:
                driver_photo = Image.open(self.driver.photo.path)
                driver_photo = driver_photo.resize((photo_size, photo_size), Image.Resampling.LANCZOS)
                # Create circular mask
                mask = Image.new('L', (photo_size, photo_size), 0)
                mask_draw = ImageDraw.Draw(mask)
                mask_draw.ellipse((0, 0, photo_size, photo_size), fill=255)
                badge_img.paste(driver_photo, (photo_x, photo_y), mask)
            except:
                # Draw placeholder circle
                draw.ellipse([photo_x, photo_y, photo_x + photo_size, photo_y + photo_size],
                           fill='#e0e0e0', outline='#cccccc', width=2)
                draw.text((photo_x + photo_size//2 - 20, photo_y + photo_size//2 - 10),
                         "PHOTO", fill='#666666', font=small_font)
        else:
            # Draw placeholder circle
            draw.ellipse([photo_x, photo_y, photo_x + photo_size, photo_y + photo_size],
                       fill='#e0e0e0', outline='#cccccc', width=2)
            draw.text((photo_x + photo_size//2 - 20, photo_y + photo_size//2 - 10),
                     "PHOTO", fill='#666666', font=small_font)

        # Add driver name
        name_y = photo_y + photo_size + 20
        driver_name = self.driver.name
        if len(driver_name) > 20:
            driver_name = driver_name[:17] + "..."
        name_bbox = draw.textbbox((0, 0), driver_name, font=name_font)
        name_width = name_bbox[2] - name_bbox[0]
        draw.text((width//2 - name_width//2, name_y), driver_name, fill='#333333', font=name_font)

        # Add car information
        car_info_y = name_y + 50
        car_plate = f"Car: {self.driver.car_plate}"
        car_bbox = draw.textbbox((0, 0), car_plate, font=info_font)
        car_width = car_bbox[2] - car_bbox[0]
        draw.text((width//2 - car_width//2, car_info_y), car_plate, fill='#555555', font=info_font)

        if self.driver.car_model:
            car_model = self.driver.car_model
            if len(car_model) > 25:
                car_model = car_model[:22] + "..."
            model_bbox = draw.textbbox((0, 0), car_model, font=info_font)
            model_width = model_bbox[2] - model_bbox[0]
            draw.text((width//2 - model_width//2, car_info_y + 25), car_model, fill='#555555', font=info_font)

        # Add QR code
        qr_size = 100
        qr_x = width//2 - qr_size//2
        qr_y = height - 200
        qr_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
        badge_img.paste(qr_resized, (qr_x, qr_y))

        # Add QR code label
        qr_label = "Scan for verification"
        qr_label_bbox = draw.textbbox((0, 0), qr_label, font=small_font)
        qr_label_width = qr_label_bbox[2] - qr_label_bbox[0]
        draw.text((width//2 - qr_label_width//2, qr_y + qr_size + 10), qr_label, fill='#666666', font=small_font)

        # Add footer with event name
        footer_y = height - 80
        for y in range(footer_y, height):
            # Create gradient footer (green theme)
            color_intensity = int(255 * ((y - footer_y) / (height - footer_y)))
            color = (30 + color_intensity//4, 120 + color_intensity//2, 60 + color_intensity//4)
            draw.line([(0, y), (width, y)], fill=color)

        # Add event name in footer
        event_name = self.driver.event.name
        if len(event_name) > 30:
            event_name = event_name[:27] + "..."
        event_bbox = draw.textbbox((0, 0), event_name, font=info_font)
        event_width = event_bbox[2] - event_bbox[0]
        draw.text((width//2 - event_width//2, footer_y + 20), event_name, fill='white', font=info_font)

        # Save badge image
        buffer = BytesIO()
        badge_img.save(buffer, format='PNG')
        buffer.seek(0)

        filename = f"badge_driver_{self.driver.id}.png"
        self.badge_image.save(filename, File(buffer), save=False)

        self.is_generated = True
        self.generated_at = timezone.now()
        self.save()

        return badge_img


class ContactPersonBadge(models.Model):
    contact_person = models.OneToOneField('contact_persons.ContactPerson', on_delete=models.CASCADE, related_name='badge')
    template = models.ForeignKey(BadgeTemplate, on_delete=models.CASCADE, null=True, blank=True)

    # QR Code
    qr_code_data = models.TextField()  # JSON data for QR code
    qr_code_image = models.ImageField(upload_to='qr_codes/', null=True, blank=True)

    # Badge File
    badge_image = models.ImageField(upload_to='generated_badges/', null=True, blank=True)

    # Generation Status
    is_generated = models.BooleanField(default=False)
    generated_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Contact Person Badge for {self.contact_person.full_name}"

    def generate_qr_code(self):
        """Generate QR code for the contact person"""
        import json
        from django.utils import timezone

        qr_data = {
            'contact_person_id': str(self.contact_person.id),
            'name': self.contact_person.full_name,
            'event': self.contact_person.event.name,
            'position': self.contact_person.position,
            'organization': self.contact_person.organization,
            'type': 'contact_person',
            'generated_at': timezone.now().isoformat()
        }

        self.qr_code_data = json.dumps(qr_data)

        # Generate QR code image
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(f"{settings.QR_CODE_BASE_URL}/verify/contact-person/{self.contact_person.id}")
        qr.make(fit=True)

        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Save QR code image
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        buffer.seek(0)

        filename = f"qr_contact_person_{self.contact_person.id}.png"
        self.qr_code_image.save(filename, File(buffer), save=False)

        return qr_img

    def generate_badge(self):
        """Generate the complete badge image for contact person"""
        from django.utils import timezone

        # Get or create QR code
        if not self.qr_code_image:
            qr_img = self.generate_qr_code()
        else:
            qr_img = Image.open(self.qr_code_image.path)

        # Get template or use default dimensions
        template = self.template or BadgeTemplate.objects.filter(is_default=True).first()
        width = template.width if template else 450
        height = template.height if template else 650

        # Create badge image with gradient background
        badge_img = Image.new('RGB', (width, height), color='#ffffff')
        draw = ImageDraw.Draw(badge_img)

        # Add header banner with gradient (different color for contact persons - purple theme)
        banner_height = 80
        for y in range(banner_height):
            # Create gradient from purple to dark purple
            color_intensity = int(255 * (1 - y / banner_height))
            color = (120 + color_intensity//4, 30 + color_intensity//4, 120 + color_intensity//2)
            draw.line([(0, y), (width, y)], fill=color)

        # Load fonts
        try:
            title_font = ImageFont.load_default()
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        except:
            title_font = ImageFont.load_default()
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()
            small_font = ImageFont.load_default()

        # Add "CONTACT PERSON" title in header
        title_text = "CONTACT PERSON"
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        draw.text((width//2 - title_width//2, 25), title_text, fill='white', font=title_font)

        # Add contact person photo placeholder or actual photo
        photo_size = 120
        photo_x = width//2 - photo_size//2
        photo_y = banner_height + 30

        if self.contact_person.photo:
            try:
                contact_photo = Image.open(self.contact_person.photo.path)
                contact_photo = contact_photo.resize((photo_size, photo_size), Image.Resampling.LANCZOS)
                # Create circular mask
                mask = Image.new('L', (photo_size, photo_size), 0)
                mask_draw = ImageDraw.Draw(mask)
                mask_draw.ellipse((0, 0, photo_size, photo_size), fill=255)
                badge_img.paste(contact_photo, (photo_x, photo_y), mask)
            except:
                # Draw placeholder circle
                draw.ellipse([photo_x, photo_y, photo_x + photo_size, photo_y + photo_size],
                           fill='#e0e0e0', outline='#cccccc', width=2)
                draw.text((photo_x + photo_size//2 - 20, photo_y + photo_size//2 - 10),
                         "PHOTO", fill='#666666', font=small_font)
        else:
            # Draw placeholder circle
            draw.ellipse([photo_x, photo_y, photo_x + photo_size, photo_y + photo_size],
                       fill='#e0e0e0', outline='#cccccc', width=2)
            draw.text((photo_x + photo_size//2 - 20, photo_y + photo_size//2 - 10),
                     "PHOTO", fill='#666666', font=small_font)

        # Add contact person name
        name_y = photo_y + photo_size + 20
        contact_name = self.contact_person.full_name
        if len(contact_name) > 20:
            contact_name = contact_name[:17] + "..."
        name_bbox = draw.textbbox((0, 0), contact_name, font=name_font)
        name_width = name_bbox[2] - name_bbox[0]
        draw.text((width//2 - name_width//2, name_y), contact_name, fill='#333333', font=name_font)

        # Add position and organization
        info_y = name_y + 50
        if self.contact_person.position:
            position = self.contact_person.position
            if len(position) > 25:
                position = position[:22] + "..."
            pos_bbox = draw.textbbox((0, 0), position, font=info_font)
            pos_width = pos_bbox[2] - pos_bbox[0]
            draw.text((width//2 - pos_width//2, info_y), position, fill='#555555', font=info_font)
            info_y += 25

        if self.contact_person.organization:
            organization = self.contact_person.organization
            if len(organization) > 25:
                organization = organization[:22] + "..."
            org_bbox = draw.textbbox((0, 0), organization, font=info_font)
            org_width = org_bbox[2] - org_bbox[0]
            draw.text((width//2 - org_width//2, info_y), organization, fill='#555555', font=info_font)

        # Add QR code
        qr_size = 100
        qr_x = width//2 - qr_size//2
        qr_y = height - 200
        qr_resized = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
        badge_img.paste(qr_resized, (qr_x, qr_y))

        # Add QR code label
        qr_label = "Scan for verification"
        qr_label_bbox = draw.textbbox((0, 0), qr_label, font=small_font)
        qr_label_width = qr_label_bbox[2] - qr_label_bbox[0]
        draw.text((width//2 - qr_label_width//2, qr_y + qr_size + 10), qr_label, fill='#666666', font=small_font)

        # Add footer with event name
        footer_y = height - 80
        for y in range(footer_y, height):
            # Create gradient footer (purple theme)
            color_intensity = int(255 * ((y - footer_y) / (height - footer_y)))
            color = (120 + color_intensity//4, 30 + color_intensity//4, 120 + color_intensity//2)
            draw.line([(0, y), (width, y)], fill=color)

        # Add event name in footer
        event_name = self.contact_person.event.name
        if len(event_name) > 30:
            event_name = event_name[:27] + "..."
        event_bbox = draw.textbbox((0, 0), event_name, font=info_font)
        event_width = event_bbox[2] - event_bbox[0]
        draw.text((width//2 - event_width//2, footer_y + 20), event_name, fill='white', font=info_font)

        # Save badge image
        buffer = BytesIO()
        badge_img.save(buffer, format='PNG')
        buffer.seek(0)

        filename = f"badge_contact_person_{self.contact_person.id}.png"
        self.badge_image.save(filename, File(buffer), save=False)

        self.is_generated = True
        self.generated_at = timezone.now()
        self.save()

        return badge_img
