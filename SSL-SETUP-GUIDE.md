# 🔐 SSL Setup Guide - University of Gondar Event Management System

This guide will help you enable HTTPS with Let's Encrypt SSL certificates for your University of Gondar Event Management System.

## 📋 Prerequisites

Before starting, ensure you have:

1. **Domain Configuration**: `event.uog.edu.et` points to your server's IP address
2. **DNS Setup**: A record configured correctly
3. **Firewall**: Ports 80 (HTTP) and 443 (HTTPS) are open
4. **Email Access**: Valid email for Let's Encrypt notifications
5. **Server Access**: Administrative access to the server

## 🚀 Quick Setup (Recommended)

### Step 1: Verify Domain Configuration

```bash
# Test if domain resolves to your server
ping event.uog.edu.et

# Check if ports are accessible
telnet event.uog.edu.et 80
telnet event.uog.edu.et 443
```

### Step 2: Run SSL Initialization

**For Windows (PowerShell):**
```powershell
# Run as Administrator
.\init-letsencrypt.ps1
```

**For Linux/Mac:**
```bash
# Make script executable
chmod +x init-letsencrypt.sh

# Run the script
./init-letsencrypt.sh
```

### Step 3: Verify SSL Installation

After the script completes:

1. **Test HTTPS Access**: Visit `https://event.uog.edu.et`
2. **Check Certificate**: Click the lock icon in your browser
3. **Verify Auto-Renewal**: Check that certbot container is running

## 🔧 Manual Setup (Advanced)

If you prefer manual setup or need to troubleshoot:

### 1. Stop Current Services

```bash
docker-compose -f docker-compose.prod.yml down
```

### 2. Create Temporary HTTP-Only Configuration

Create `nginx/conf.d/temp-ssl.conf`:

```nginx
server {
    listen 80;
    server_name event.uog.edu.et www.event.uog.edu.et;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files $uri =404;
    }

    location / {
        return 200 'SSL setup in progress...';
        add_header Content-Type text/plain;
    }
}
```

### 3. Start Nginx for Certificate Generation

```bash
# Backup current config
cp nginx/conf.d/default.conf nginx/conf.d/default.conf.backup

# Use temporary config
mv nginx/conf.d/default.conf nginx/conf.d/default.conf.ssl

# Start nginx
docker-compose -f docker-compose.prod.yml up -d nginx
```

### 4. Generate SSL Certificate

```bash
# Generate certificate
docker-compose -f docker-compose.prod.yml run --rm certbot \
    certonly \
    --webroot \
    --webroot-path=/var/www/certbot \
    --email <EMAIL> \
    --agree-tos \
    --no-eff-email \
    --force-renewal \
    -d event.uog.edu.et \
    -d www.event.uog.edu.et
```

### 5. Enable SSL Configuration

```bash
# Restore SSL-enabled config
mv nginx/conf.d/default.conf.ssl nginx/conf.d/default.conf
rm nginx/conf.d/temp-ssl.conf

# Restart all services
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d
```

## 🔄 Certificate Renewal

SSL certificates are automatically renewed every 12 hours via the certbot container.

### Manual Renewal

**Windows:**
```powershell
docker-compose -f docker-compose.prod.yml run --rm certbot renew
docker-compose -f docker-compose.prod.yml exec nginx nginx -s reload
```

**Linux/Mac:**
```bash
./renew-ssl.sh
```

### Check Certificate Status

```bash
docker-compose -f docker-compose.prod.yml run --rm certbot certificates
```

## 🛡️ Security Features Enabled

The SSL configuration includes:

- **TLS 1.2 & 1.3**: Modern encryption protocols
- **HSTS**: HTTP Strict Transport Security
- **OCSP Stapling**: Certificate validation optimization
- **Security Headers**: XSS protection, content type sniffing prevention
- **CSP**: Content Security Policy
- **Perfect Forward Secrecy**: Enhanced encryption

## 🧪 Testing SSL Configuration

### 1. Browser Test
- Visit `https://event.uog.edu.et`
- Check for green lock icon
- Verify certificate details

### 2. SSL Labs Test
- Visit: https://www.ssllabs.com/ssltest/
- Enter: `event.uog.edu.et`
- Aim for A+ rating

### 3. Command Line Test
```bash
# Test SSL connection
openssl s_client -connect event.uog.edu.et:443 -servername event.uog.edu.et

# Check certificate expiry
echo | openssl s_client -connect event.uog.edu.et:443 2>/dev/null | openssl x509 -noout -dates
```

## 🚨 Troubleshooting

### Common Issues

**1. Domain Not Accessible**
- Check DNS A record
- Verify firewall settings
- Ensure domain propagation

**2. Certificate Generation Failed**
- Check domain accessibility
- Verify email address
- Review certbot logs: `./logs/certbot/`

**3. HTTPS Not Working**
- Check nginx configuration
- Verify certificate paths
- Review nginx logs: `./logs/nginx/`

**4. Mixed Content Warnings**
- Update all HTTP links to HTTPS
- Check browser console for errors

### Log Locations

- **Nginx Logs**: `./logs/nginx/`
- **Certbot Logs**: `./logs/certbot/`
- **SSL Access Logs**: `./logs/nginx/access_ssl.log`
- **SSL Error Logs**: `./logs/nginx/error_ssl.log`

## 📞 Support

For issues with SSL setup:

1. **Check Logs**: Review error logs in `./logs/`
2. **Verify Configuration**: Ensure all prerequisites are met
3. **Test Manually**: Use the manual setup steps
4. **Contact Support**: Provide logs and error messages

## 🎯 Best Practices

1. **Regular Monitoring**: Check certificate expiry monthly
2. **Backup Certificates**: Store certificates securely
3. **Update Dependencies**: Keep Docker images updated
4. **Security Scanning**: Regular SSL configuration testing
5. **Documentation**: Keep SSL setup documentation updated

---

**🔐 Your University of Gondar Event Management System will be secured with enterprise-grade SSL encryption! 🔐**
