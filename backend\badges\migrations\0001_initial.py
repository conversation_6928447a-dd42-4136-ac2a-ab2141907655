# Generated by Django 4.2.7 on 2025-07-26 04:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("participants", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="BadgeTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                ("width", models.IntegerField(default=400)),
                ("height", models.IntegerField(default=600)),
                ("background_color", models.CharField(default="#ffffff", max_length=7)),
                (
                    "template_file",
                    models.ImageField(
                        blank=True, null=True, upload_to="badge_templates/"
                    ),
                ),
                ("is_default", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Badge",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("qr_code_data", models.TextField()),
                (
                    "qr_code_image",
                    models.ImageField(blank=True, null=True, upload_to="qr_codes/"),
                ),
                (
                    "badge_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="generated_badges/"
                    ),
                ),
                ("is_generated", models.BooleanField(default=False)),
                ("generated_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "participant",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="badge",
                        to="participants.participant",
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="badges.badgetemplate",
                    ),
                ),
            ],
        ),
    ]
