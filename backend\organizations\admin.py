from django.contrib import admin
from .models import Organization, OrganizationSettings


class OrganizationSettingsInline(admin.StackedInline):
    model = OrganizationSettings
    extra = 0
    fieldsets = (
        ('Event Settings', {
            'fields': ('default_event_duration_hours', 'default_registration_fee')
        }),
        ('Email Settings', {
            'fields': ('email_signature',)
        }),
        ('Branding Settings', {
            'fields': ('primary_color', 'secondary_color')
        }),
        ('Notification Settings', {
            'fields': ('send_welcome_emails', 'send_confirmation_emails', 'send_reminder_emails')
        }),
    )


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'short_name', 'email', 'phone', 'city', 'country', 
        'is_active', 'is_primary', 'created_at'
    ]
    list_filter = ['is_active', 'is_primary', 'country', 'created_at']
    search_fields = ['name', 'short_name', 'email', 'city']
    readonly_fields = ['created_at', 'updated_at', 'full_address', 'display_name']
    inlines = [OrganizationSettingsInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'short_name', 'logo', 'motto', 'description')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'website')
        }),
        ('Address', {
            'fields': (
                'address_line_1', 'address_line_2', 'city', 
                'state_province', 'postal_code', 'country', 'full_address'
            )
        }),
        ('Social Media', {
            'fields': ('facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('is_active', 'is_primary')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Ensure only one primary organization"""
        if obj.is_primary:
            Organization.objects.filter(is_primary=True).exclude(pk=obj.pk).update(is_primary=False)
        super().save_model(request, obj, form, change)


@admin.register(OrganizationSettings)
class OrganizationSettingsAdmin(admin.ModelAdmin):
    list_display = [
        'organization', 'default_event_duration_hours', 'default_registration_fee',
        'primary_color', 'secondary_color', 'updated_at'
    ]
    list_filter = ['send_welcome_emails', 'send_confirmation_emails', 'send_reminder_emails']
    search_fields = ['organization__name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Organization', {
            'fields': ('organization',)
        }),
        ('Event Settings', {
            'fields': ('default_event_duration_hours', 'default_registration_fee')
        }),
        ('Email Settings', {
            'fields': ('email_signature',)
        }),
        ('Branding Settings', {
            'fields': ('primary_color', 'secondary_color')
        }),
        ('Notification Settings', {
            'fields': ('send_welcome_emails', 'send_confirmation_emails', 'send_reminder_emails')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
