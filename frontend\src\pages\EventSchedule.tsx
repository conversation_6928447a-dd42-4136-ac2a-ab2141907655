import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, Col, Button, Spinner, Alert } from 'react-bootstrap';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { eventService, Event, EventSchedule as EventScheduleType } from '../services/api';
import DailySchedule from '../components/DailySchedule';

const EventSchedule: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [event, setEvent] = useState<Event | null>(null);
  const [schedules, setSchedules] = useState<EventScheduleType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (id) {
      fetchEventData();
    }
  }, [id]);

  const fetchEventData = async () => {
    try {
      const [eventResponse, scheduleResponse] = await Promise.all([
        eventService.getEvent(parseInt(id!)),
        eventService.getEventSchedule(parseInt(id!)),
      ]);

      setEvent(eventResponse.data);
      setSchedules(scheduleResponse.data);
    } catch (error) {
      console.error('Error fetching event data:', error);
      setError('Failed to load event schedule');
    } finally {
      setLoading(false);
    }
  };



  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading event schedule...</p>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-4">
        <Alert variant="danger">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      </Container>
    );
  }

  if (!event) {
    return (
      <Container className="py-4">
        <Alert variant="warning">
          <i className="fas fa-question-circle me-2"></i>
          Event not found
        </Alert>
      </Container>
    );
  }



  return (
    <Container className="py-4">
      {/* Event Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-start">
            <div>
              <h1 className="display-5 fw-bold mb-2">{event.name}</h1>
              <p className="lead text-muted mb-3">{event.description}</p>
              <div className="d-flex flex-wrap gap-3 mb-3">
                <div>
                  <i className="fas fa-calendar text-primary me-2"></i>
                  <strong>
                    {new Date(event.start_date).toLocaleDateString()} - {' '}
                    {new Date(event.end_date).toLocaleDateString()}
                  </strong>
                </div>
                <div>
                  <i className="fas fa-map-marker-alt text-primary me-2"></i>
                  <strong>{event.location}, {event.city}</strong>
                </div>
                <div>
                  <i className="fas fa-users text-primary me-2"></i>
                  <strong>{event.participant_count} participants</strong>
                </div>
              </div>
            </div>
            <div>
              <Link to={`/events/${event.id}`} className="btn btn-outline-primary me-2">
                <i className="fas fa-arrow-left me-2"></i>
                Back to Event
              </Link>
              <Link to={`/events/${event.id}/gallery`} className="btn btn-primary">
                <i className="fas fa-images me-2"></i>
                Gallery
              </Link>
            </div>
          </div>
        </Col>
      </Row>

      {/* Daily Schedule */}
      <DailySchedule schedules={schedules} eventName={event?.name || 'Event'} />

      {/* Download Schedule Button */}
      {schedules.length > 0 && (
        <Row className="mt-5">
          <Col className="text-center">
            <Button
              variant="success"
              size="lg"
              onClick={() => {
                // This could generate and download a PDF schedule
                alert('Schedule download feature coming soon!');
              }}
            >
              <i className="fas fa-download me-2"></i>
              Download Schedule (PDF)
            </Button>
          </Col>
        </Row>
      )}
    </Container>
  );
};

export default EventSchedule;
