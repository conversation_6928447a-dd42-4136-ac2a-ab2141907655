import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Badge as BSBadge } from 'react-bootstrap';
import api from '../services/api';

interface Badge {
  id: number;
  participant: {
    id: number;
    full_name: string;
    participant_type: {
      name: string;
    };
    institution_name: string;
    position?: string;
  };
  badge_image: string;
  is_generated: boolean;
  generated_at: string;
}

interface Event {
  id: number;
  name: string;
  sponsors: Array<{
    id: number;
    name: string;
    sponsor_type: string;
    logo: string;
  }>;
  organizers: Array<{
    id: number;
    name: string;
    title: string;
    photo: string;
    is_primary: boolean;
  }>;
}

const BadgePreview: React.FC = () => {
  const [badges, setBadges] = useState<Badge[]>([]);
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBadges();
    fetchEventDetails();
  }, []);

  const fetchBadges = async () => {
    try {
      const response = await api.get('/badges/');
      setBadges(response.data.results || response.data);
    } catch (err) {
      setError('Failed to fetch badges');
      console.error('Error fetching badges:', err);
    }
  };

  const fetchEventDetails = async () => {
    try {
      const response = await api.get('/events/');
      const events = response.data.results || response.data;
      if (events.length > 0) {
        const eventId = events[0].id;
        
        // Fetch sponsors and organizers
        const [sponsorsRes, organizersRes] = await Promise.all([
          api.get(`/events/${eventId}/sponsors/`),
          api.get(`/events/${eventId}/organizers/`)
        ]);
        
        setEvent({
          ...events[0],
          sponsors: sponsorsRes.data.results || sponsorsRes.data,
          organizers: organizersRes.data.results || organizersRes.data
        });
      }
    } catch (err) {
      console.error('Error fetching event details:', err);
    } finally {
      setLoading(false);
    }
  };

  const downloadBadge = (badge: Badge) => {
    if (badge.badge_image) {
      const link = document.createElement('a');
      link.href = badge.badge_image;
      const participantName = badge.participant?.full_name || 'Unknown_Participant';
      link.download = `badge_${participantName.replace(/\s+/g, '_')}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const downloadAllBadges = async () => {
    const validBadges = badges.filter(badge => badge.badge_image);

    if (validBadges.length === 0) {
      alert('No badges available for download');
      return;
    }

    // Offer two download options
    const choice = window.confirm(
      `Choose download method:\n\nOK = Individual downloads (${validBadges.length} files)\nCancel = View all badges in new tab for manual saving`
    );

    if (choice) {
      // Option 1: Individual downloads with staggered timing
      validBadges.forEach((badge, index) => {
        setTimeout(() => {
          downloadBadge(badge);
        }, index * 800); // 800ms delay between downloads
      });
      alert(`Started downloading ${validBadges.length} badges. Please wait for all downloads to complete.`);
    } else {
      // Option 2: Open all badges in a new tab for manual saving
      createBadgeGalleryPage(validBadges);
    }
  };

  const createBadgeGalleryPage = (validBadges: Badge[]) => {
    // Create HTML content with all badges
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>All Event Badges - ${event?.name || 'Event'}</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
          .header { text-align: center; margin-bottom: 30px; }
          .badge-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
          .badge-item { background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
          .badge-image { max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; }
          .badge-name { margin: 10px 0 5px 0; font-weight: bold; color: #333; }
          .download-btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px; }
          .download-btn:hover { background: #0056b3; }
          .instructions { background: #e7f3ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>All Event Badges</h1>
          <h2>${event?.name || 'Event'}</h2>
          <div class="instructions">
            <strong>Instructions:</strong> Right-click on any badge image and select "Save image as..." to download individual badges.
            Or use the download buttons below each badge.
          </div>
        </div>
        <div class="badge-grid">
          ${validBadges.map(badge => {
            const participantName = badge.participant?.full_name || 'Unknown Participant';
            const institution = badge.participant?.institution_name || 'Unknown Institution';
            return `
              <div class="badge-item">
                <div class="badge-name">${participantName}</div>
                <div style="color: #666; font-size: 0.9em; margin-bottom: 10px;">${institution}</div>
                <img src="${badge.badge_image}" alt="Badge for ${participantName}" class="badge-image" />
                <br>
                <button class="download-btn" onclick="downloadImage('${badge.badge_image}', 'badge_${participantName.replace(/\s+/g, '_')}.png')">
                  Download Badge
                </button>
              </div>
            `;
          }).join('')}
        </div>
        <script>
          function downloadImage(url, filename) {
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        </script>
      </body>
      </html>
    `;

    // Open in new tab
    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.write(htmlContent);
      newWindow.document.close();
    } else {
      alert('Please allow popups to view the badge gallery');
    }
  };

  const getSponsorTypeColor = (type: string) => {
    switch (type) {
      case 'platinum': return 'warning';
      case 'gold': return 'warning';
      case 'silver': return 'secondary';
      case 'bronze': return 'dark';
      default: return 'primary';
    }
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center mb-3">
            <i className="fas fa-id-card me-2 text-primary" style={{fontSize: '24px'}}></i>
            <h2 className="mb-0">🎨 PREMIUM Academic Badge with Wave Design</h2>
            <span className="badge bg-success ms-2">90mm × 140mm</span>
            <span className="badge bg-warning ms-2">Premium</span>
            <span className="badge bg-primary ms-2">300 DPI</span>
          </div>
          <p className="text-muted">
            <strong>Premium academic badges with elegant wave motifs, gold gradients, Georgia serif fonts, and precise element placement</strong>
            <br />
            <small>✨ Premium Features: Wave Pattern Banner | Gold Gradient Role Badge | Dashed Photo Frame | Wave QR Border | Georgia Serif Typography | Academic Elegance</small>
          </p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      {/* Event Information */}
      {event && (
        <Row className="mb-4">
          <Col md={6}>
            <Card className="h-100">
              <Card.Header className="bg-primary text-white">
                <i className="fas fa-handshake me-2"></i>
                Sponsors ({event.sponsors.length})
              </Card.Header>
              <Card.Body>
                {event.sponsors && event.sponsors.length > 0 ? (
                  <div className="d-flex flex-wrap gap-2">
                    {event.sponsors.map(sponsor => (
                      <div key={sponsor.id} className="text-center">
                        <BSBadge bg={getSponsorTypeColor(sponsor.sponsor_type)} className="mb-1">
                          {sponsor.sponsor_type ? sponsor.sponsor_type.toUpperCase() : 'SPONSOR'}
                        </BSBadge>
                        <div className="small">{sponsor.name || 'Unknown Sponsor'}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted mb-0">No sponsors added yet</p>
                )}
              </Card.Body>
            </Card>
          </Col>
          <Col md={6}>
            <Card className="h-100">
              <Card.Header className="bg-success text-white">
                <i className="fas fa-users me-2"></i>
                Organizers ({event.organizers.length})
              </Card.Header>
              <Card.Body>
                {event.organizers && event.organizers.length > 0 ? (
                  <div className="d-flex flex-wrap gap-2">
                    {event.organizers.map(organizer => (
                      <div key={organizer.id} className="text-center">
                        {organizer.is_primary && (
                          <BSBadge bg="danger" className="mb-1">PRIMARY</BSBadge>
                        )}
                        <div className="small fw-bold">{organizer.name || 'Unknown Organizer'}</div>
                        <div className="small text-muted">{organizer.title || 'No Title'}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted mb-0">No organizers added yet</p>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Badge Features */}
      <Row className="mb-4">
        <Col>
          <Card className="border-primary">
            <Card.Header className="bg-gradient" style={{background: 'linear-gradient(135deg, #d4af37 0%, #fde047 100%)', color: '#1e3a8a'}}>
              <h5 className="mb-0">✨ PREMIUM Academic Badge with Wave Design</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={3}>
                  <div className="text-center p-3 border-end">
                    <i className="fas fa-water text-primary mb-2" style={{fontSize: '32px'}}></i>
                    <h6>🌊 Wave Pattern Banner</h6>
                    <small className="text-muted">
                      <strong>Elegant Wave Motifs:</strong><br/>
                      Faded blue banner with decorative wave patterns ending at photo midpoint
                    </small>
                  </div>
                </Col>
                <Col md={3}>
                  <div className="text-center p-3 border-end">
                    <i className="fas fa-circle-notch text-success mb-2" style={{fontSize: '32px'}}></i>
                    <h6>⭕ Dashed Photo Frame</h6>
                    <small className="text-muted">
                      <strong>Wave-Inspired Design:</strong><br/>
                      Gold-dashed circular frame with elegant wave motifs
                    </small>
                  </div>
                </Col>
                <Col md={3}>
                  <div className="text-center p-3 border-end">
                    <i className="fas fa-crown text-warning mb-2" style={{fontSize: '32px'}}></i>
                    <h6>👑 Gold Role Badge</h6>
                    <small className="text-muted">
                      <strong>Premium Gradient:</strong><br/>
                      Gold gradient role badge with Georgia serif typography
                    </small>
                  </div>
                </Col>
                <Col md={3}>
                  <div className="text-center p-3">
                    <i className="fas fa-graduation-cap text-warning mb-2" style={{fontSize: '32px'}}></i>
                    <h6>🎓 Academic Excellence</h6>
                    <small className="text-muted">
                      <strong>Premium Features:</strong><br/>
                      Georgia serif fonts, wave separators, dot texture, academic elegance
                    </small>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Badge Gallery */}
      <Row>
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h4 className="mb-0">
              🎨 Premium Academic Badges with Wave Design ({badges.length})
              <span className="badge bg-primary ms-2">1063x1654px</span>
              <span className="badge bg-success ms-2">90mm × 140mm</span>
              <span className="badge bg-warning ms-2">Premium</span>
            </h4>
            <div className="d-flex align-items-center gap-3">
              <div>
                <span className="badge bg-success me-2">{event?.sponsors?.length || 0} Sponsors</span>
                <span className="badge bg-info">{event?.organizers?.length || 0} Organizers</span>
              </div>
              <Button
                variant="success"
                size="sm"
                onClick={downloadAllBadges}
                disabled={badges.filter(b => b.badge_image).length === 0}
              >
                <i className="fas fa-download me-1"></i>
                Download All ({badges.filter(b => b.badge_image).length})
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      <Row>
        {badges.length > 0 ? (
          badges.map(badge => (
            <Col key={badge.id} lg={4} md={6} className="mb-4">
              <Card className="h-100 shadow-sm">
                <Card.Header className="bg-light">
                  <div className="d-flex justify-content-between align-items-center">
                    <h6 className="mb-0">{badge.participant?.full_name || 'Unknown Participant'}</h6>
                    <BSBadge bg="primary">{badge.participant?.participant_type?.name || 'Unknown Type'}</BSBadge>
                  </div>
                </Card.Header>
                <Card.Body className="text-center">
                  {badge.badge_image ? (
                    <div>
                      <img
                        src={badge.badge_image}
                        alt={`Enhanced Badge for ${badge.participant.full_name}`}
                        className="img-fluid mb-3 shadow"
                        style={{
                          maxHeight: '400px',
                          border: '2px solid #007bff',
                          borderRadius: '12px',
                          boxShadow: '0 8px 16px rgba(0,123,255,0.2)'
                        }}
                      />
                      <div className="small text-muted mb-2">
                        <strong>Institution:</strong> {badge.participant?.institution_name || 'Unknown Institution'}
                      </div>
                      {badge.participant?.position && (
                        <div className="small text-muted mb-3">
                          <strong>Position:</strong> {badge.participant.position}
                        </div>
                      )}
                      <div className="d-grid gap-2">
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => downloadBadge(badge)}
                        >
                          <i className="fas fa-download me-1"></i>
                          Download Badge
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-muted">
                      <i className="fas fa-eye mb-2" style={{fontSize: '32px'}}></i>
                      <p>Badge not generated yet</p>
                    </div>
                  )}
                </Card.Body>
                {badge.generated_at && (
                  <Card.Footer className="small text-muted">
                    Generated: {new Date(badge.generated_at).toLocaleString()}
                  </Card.Footer>
                )}
              </Card>
            </Col>
          ))
        ) : (
          <Col>
            <Alert variant="info">
              <h5>No badges found</h5>
              <p>Generate some badges first to see the enhanced design with sponsors and organizers.</p>
            </Alert>
          </Col>
        )}
      </Row>
    </Container>
  );
};

export default BadgePreview;
