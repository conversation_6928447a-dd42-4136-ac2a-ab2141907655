import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Badge, Spinner, <PERSON><PERSON>, Tab, Tabs, Table, Nav } from 'react-bootstrap';
import { useParams, Link } from 'react-router-dom';
import { eventService, participantService, Event, EventSchedule, EventGallery, Participant, getMediaUrl } from '../services/api';

const EventDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [event, setEvent] = useState<Event | null>(null);
  const [schedules, setSchedules] = useState<EventSchedule[]>([]);
  const [gallery, setGallery] = useState<EventGallery[]>([]);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (id) {
      fetchEventData();
    }
  }, [id]);

  const fetchEventData = async () => {
    try {
      const [eventResponse, scheduleResponse, galleryResponse, participantsResponse] = await Promise.all([
        eventService.getEvent(parseInt(id!)),
        eventService.getEventSchedule(parseInt(id!)),
        eventService.getEventGallery(parseInt(id!)),
        eventService.getEventParticipants(parseInt(id!)),
      ]);

      setEvent(eventResponse.data);
      setSchedules(scheduleResponse.data);
      setGallery(galleryResponse.data.slice(0, 6)); // Show only first 6 images
      setParticipants(participantsResponse.data.slice(0, 12)); // Show only first 12 participants
    } catch (error) {
      console.error('Error fetching event data:', error);
      setError('Failed to load event details');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading event details...</p>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-4">
        <Alert variant="danger">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      </Container>
    );
  }

  if (!event) {
    return (
      <Container className="py-4">
        <Alert variant="warning">
          <i className="fas fa-question-circle me-2"></i>
          Event not found
        </Alert>
      </Container>
    );
  }

  const upcomingSchedules = schedules.slice(0, 3);
  const featuredGallery = gallery.filter(img => img.is_featured).slice(0, 3);
  const recentGallery = featuredGallery.length > 0 ? featuredGallery : gallery.slice(0, 3);

  const getEventStatus = (event: Event) => {
    const now = new Date();
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);

    if (!event.is_active) {
      return <Badge bg="secondary">Inactive</Badge>;
    } else if (startDate > now) {
      return <Badge bg="info">Upcoming</Badge>;
    } else if (startDate <= now && endDate >= now) {
      return <Badge bg="success">Ongoing</Badge>;
    } else {
      return <Badge bg="warning">Completed</Badge>;
    }
  };

  const getParticipantStats = () => {
    const confirmed = participants.filter(p => p.is_confirmed).length;
    const pending = participants.filter(p => !p.is_confirmed).length;
    const withHotel = participants.filter(p => p.assigned_hotel).length;
    const withDriver = participants.filter(p => p.assigned_driver).length;

    return { confirmed, pending, withHotel, withDriver, total: participants.length };
  };

  const stats = getParticipantStats();

  return (
    <Container className="py-4">
      {/* Management Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-start mb-3">
            <div>
              <Link to="/events" className="btn btn-outline-secondary mb-3">
                <i className="fas fa-arrow-left me-2"></i>
                Back to Events
              </Link>
              <h1 className="display-5 fw-bold text-primary mb-2">{event.name}</h1>
              <div className="d-flex gap-2 mb-3">
                {getEventStatus(event)}
                <Badge bg="primary">
                  <i className="fas fa-users me-1"></i>
                  {stats.total} Registered
                </Badge>
                <Badge bg="info">
                  <i className="fas fa-calendar me-1"></i>
                  {new Date(event.start_date).toLocaleDateString()}
                </Badge>
              </div>
            </div>
            <div className="d-flex gap-2">
              <Link to={`/events/${event.id}/edit`} className="btn btn-outline-primary">
                <i className="fas fa-edit me-2"></i>
                Edit Event
              </Link>
              <Link to={`/participants/manage?event=${event.id}`} className="btn btn-primary">
                <i className="fas fa-users me-2"></i>
                Manage Participants
              </Link>
            </div>
          </div>
        </Col>
      </Row>

      {/* Event Header */}
      <Row className="mb-5">
        <Col>
          {event.banner && (
            <div className="position-relative mb-4">
              <img
                src={getMediaUrl(event.banner)}
                alt={event.name}
                className="img-fluid rounded shadow"
                style={{ width: '100%', height: '300px', objectFit: 'cover' }}
              />
              <div className="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25 rounded d-flex align-items-end">
                <div className="p-4 text-white">
                  <h1 className="display-4 fw-bold mb-2">{event.name}</h1>
                  <p className="lead mb-0">{event.description}</p>
                </div>
              </div>
            </div>
          )}

          {!event.banner && (
            <div className="text-center mb-4">
              <h1 className="display-4 fw-bold mb-2">{event.name}</h1>
              <p className="lead text-muted">{event.description}</p>
            </div>
          )}
        </Col>
      </Row>

      {/* Event Info Cards */}
      <Row className="mb-5">
        <Col md={3}>
          <Card className="text-center border-primary h-100">
            <Card.Body>
              <i className="fas fa-calendar-alt text-primary fa-2x mb-3"></i>
              <h6 className="text-muted">Duration</h6>
              <p className="fw-bold">
                {new Date(event.start_date).toLocaleDateString()} - {' '}
                {new Date(event.end_date).toLocaleDateString()}
              </p>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3}>
          <Card className="text-center border-success h-100">
            <Card.Body>
              <i className="fas fa-map-marker-alt text-success fa-2x mb-3"></i>
              <h6 className="text-muted">Location</h6>
              <p className="fw-bold">{event.location}</p>
              <small className="text-muted">{event.city}, {event.country}</small>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3}>
          <Card className="text-center border-info h-100">
            <Card.Body>
              <i className="fas fa-users text-info fa-2x mb-3"></i>
              <h6 className="text-muted">Participants</h6>
              <p className="fw-bold">{event.participant_count}</p>
              <small className="text-muted">Registered</small>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3}>
          <Card className="text-center border-warning h-100">
            <Card.Body>
              <i className="fas fa-user text-warning fa-2x mb-3"></i>
              <h6 className="text-muted">Organizer</h6>
              <p className="fw-bold">{event.organizer_name}</p>
              <small className="text-muted">
                <a href={`mailto:${event.organizer_email}`}>
                  {event.organizer_email}
                </a>
              </small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Action Buttons */}
      <Row className="mb-5">
        <Col className="text-center">
          <div className="d-flex justify-content-center gap-3 flex-wrap">
            <Link to="/register" className="btn btn-primary btn-lg">
              <i className="fas fa-user-plus me-2"></i>
              Register Now
            </Link>
            <Link to={`/events/${event.id}/schedule`} className="btn btn-outline-primary btn-lg">
              <i className="fas fa-clock me-2"></i>
              View Schedule
            </Link>
            <Link to={`/events/${event.id}/gallery`} className="btn btn-outline-primary btn-lg">
              <i className="fas fa-images me-2"></i>
              Photo Gallery
            </Link>
            <Button variant="outline-secondary" size="lg" disabled>
              <i className="fas fa-map me-2"></i>
              Event Map
            </Button>
          </div>
        </Col>
      </Row>

      {/* Content Tabs */}
      <Tabs defaultActiveKey="overview" className="mb-4">
        <Tab eventKey="overview" title="Overview">
          <Row>
            <Col lg={8}>
              <Card className="mb-4">
                <Card.Header>
                  <h5 className="mb-0">
                    <i className="fas fa-info-circle me-2"></i>
                    About This Event
                  </h5>
                </Card.Header>
                <Card.Body>
                  <p>{event.description}</p>

                  <h6 className="mt-4 mb-3">Event Details</h6>
                  <Row>
                    <Col sm={6}>
                      <strong>Start Date:</strong><br />
                      {new Date(event.start_date).toLocaleString()}
                    </Col>
                    <Col sm={6}>
                      <strong>End Date:</strong><br />
                      {new Date(event.end_date).toLocaleString()}
                    </Col>
                  </Row>

                  <Row className="mt-3">
                    <Col sm={6}>
                      <strong>Venue:</strong><br />
                      {event.location}
                    </Col>
                    <Col sm={6}>
                      <strong>City:</strong><br />
                      {event.city}, {event.country}
                    </Col>
                  </Row>

                  {(event.latitude && event.longitude) && (
                    <div className="mt-4">
                      <h6>Location Map</h6>
                      <div className="bg-light rounded p-3 text-center">
                        <i className="fas fa-map-marked-alt fa-3x text-muted mb-2"></i>
                        <p className="text-muted mb-0">
                          Interactive map coming soon<br />
                          <small>Coordinates: {event.latitude}, {event.longitude}</small>
                        </p>
                      </div>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>

            <Col lg={4}>
              <Card className="mb-4">
                <Card.Header>
                  <h6 className="mb-0">
                    <i className="fas fa-phone me-2"></i>
                    Contact Information
                  </h6>
                </Card.Header>
                <Card.Body>
                  <div className="mb-3">
                    <strong>Organizer:</strong><br />
                    {event.organizer_name}
                  </div>
                  <div className="mb-3">
                    <strong>Email:</strong><br />
                    <a href={`mailto:${event.organizer_email}`}>
                      {event.organizer_email}
                    </a>
                  </div>
                  <div>
                    <strong>Phone:</strong><br />
                    <a href={`tel:${event.organizer_phone}`}>
                      {event.organizer_phone}
                    </a>
                  </div>
                </Card.Body>
              </Card>

              <Card>
                <Card.Header>
                  <h6 className="mb-0">
                    <i className="fas fa-share-alt me-2"></i>
                    Share Event
                  </h6>
                </Card.Header>
                <Card.Body>
                  <div className="d-grid gap-2">
                    <Button
                      variant="outline-primary"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(window.location.href);
                        alert('Event URL copied to clipboard!');
                      }}
                    >
                      <i className="fas fa-copy me-2"></i>
                      Copy Link
                    </Button>
                    <Button
                      variant="outline-info"
                      size="sm"
                      onClick={() => {
                        const text = `Check out ${event.name} - ${event.description}`;
                        const url = window.location.href;
                        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`);
                      }}
                    >
                      <i className="fab fa-twitter me-2"></i>
                      Share on Twitter
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Tab>

        <Tab eventKey="schedule" title={`Schedule (${schedules.length})`}>
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">
                <i className="fas fa-clock me-2"></i>
                Upcoming Sessions
              </h5>
              <Link to={`/events/${event.id}/schedule`} className="btn btn-primary btn-sm">
                View Full Schedule
              </Link>
            </Card.Header>
            <Card.Body>
              {upcomingSchedules.length === 0 ? (
                <div className="text-center py-4">
                  <i className="fas fa-calendar-times text-muted fa-3x mb-3"></i>
                  <p className="text-muted">No schedule available yet</p>
                </div>
              ) : (
                upcomingSchedules.map((schedule) => (
                  <div key={schedule.id} className="border-bottom pb-3 mb-3 last:border-0 last:pb-0 last:mb-0">
                    <div className="d-flex justify-content-between align-items-start">
                      <div>
                        <h6 className="mb-1">{schedule.title}</h6>
                        <p className="text-muted small mb-2">{schedule.description}</p>
                        <div className="d-flex gap-3 small text-muted">
                          <span>
                            <i className="fas fa-clock me-1"></i>
                            {new Date(schedule.start_time).toLocaleString()}
                          </span>
                          {schedule.location && (
                            <span>
                              <i className="fas fa-map-marker-alt me-1"></i>
                              {schedule.location}
                            </span>
                          )}
                          {schedule.speaker && (
                            <span>
                              <i className="fas fa-user me-1"></i>
                              {schedule.speaker}
                            </span>
                          )}
                        </div>
                      </div>
                      {schedule.is_break && (
                        <Badge bg="warning">
                          <i className="fas fa-coffee me-1"></i>
                          Break
                        </Badge>
                      )}
                    </div>
                  </div>
                ))
              )}
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="gallery" title={`Gallery (${gallery.length})`}>
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">
                <i className="fas fa-images me-2"></i>
                Event Photos
              </h5>
              <Link to={`/events/${event.id}/gallery`} className="btn btn-primary btn-sm">
                View All Photos
              </Link>
            </Card.Header>
            <Card.Body>
              {recentGallery.length === 0 ? (
                <div className="text-center py-4">
                  <i className="fas fa-images text-muted fa-3x mb-3"></i>
                  <p className="text-muted">No photos available yet</p>
                </div>
              ) : (
                <Row>
                  {recentGallery.map((image) => (
                    <Col md={4} key={image.id} className="mb-3">
                      <div className="position-relative">
                        <img
                          src={getMediaUrl(image.image)}
                          alt={image.title}
                          className="img-fluid rounded shadow-sm"
                          style={{ width: '100%', height: '200px', objectFit: 'cover' }}
                        />
                        {image.is_featured && (
                          <Badge
                            bg="warning"
                            className="position-absolute top-0 start-0 m-2"
                          >
                            <i className="fas fa-star me-1"></i>
                            Featured
                          </Badge>
                        )}
                        <div className="mt-2">
                          <h6 className="small mb-1">{image.title}</h6>
                          <small className="text-muted">
                            {new Date(image.uploaded_at).toLocaleDateString()}
                          </small>
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              )}
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="participants" title={`Participants (${participants.length})`}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-users me-2"></i>
                Registered Participants
              </h5>
            </Card.Header>
            <Card.Body>
              {participants.length === 0 ? (
                <div className="text-center py-4">
                  <i className="fas fa-user-times text-muted fa-3x mb-3"></i>
                  <p className="text-muted">No participants registered yet</p>
                </div>
              ) : (
                <Row>
                  {participants.map((participant) => (
                    <Col lg={6} key={participant.id} className="mb-3">
                      <div className="d-flex align-items-center p-3 border rounded">
                        <div className="me-3">
                          <div
                            className="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                            style={{
                              width: '50px',
                              height: '50px',
                              backgroundColor: participant.participant_type_color
                            }}
                          >
                            {participant.full_name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                          </div>
                        </div>
                        <div className="flex-grow-1">
                          <h6 className="mb-1">{participant.full_name}</h6>
                          <p className="text-muted small mb-1">{participant.position}</p>
                          <p className="text-muted small mb-0">{participant.institution_name}</p>
                        </div>
                        <Badge
                          style={{ backgroundColor: participant.participant_type_color }}
                          className="ms-2"
                        >
                          {participant.participant_type_name}
                        </Badge>
                      </div>
                    </Col>
                  ))}
                </Row>
              )}

              {participants.length > 0 && (
                <div className="text-center mt-4">
                  <Link to="/participants" className="btn btn-outline-primary">
                    <i className="fas fa-users me-2"></i>
                    View All Participants
                  </Link>
                </div>
              )}
            </Card.Body>
          </Card>
        </Tab>
      </Tabs>
    </Container>
  );
};

export default EventDetail;
