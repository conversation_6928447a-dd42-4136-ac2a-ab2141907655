import React from 'react';
import { Row, Col, Card, Badge } from 'react-bootstrap';
import { EventSchedule } from '../services/api';

interface DailyScheduleProps {
  schedules: EventSchedule[];
  eventName: string;
}

const DailySchedule: React.FC<DailyScheduleProps> = ({ schedules, eventName }) => {
  // Group schedules by date
  const groupSchedulesByDate = (schedules: EventSchedule[]) => {
    const grouped: { [key: string]: EventSchedule[] } = {};
    
    schedules.forEach(schedule => {
      const date = new Date(schedule.start_time).toDateString();
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(schedule);
    });
    
    return grouped;
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const durationMs = end.getTime() - start.getTime();
    const durationMinutes = Math.floor(durationMs / (1000 * 60));
    
    if (durationMinutes < 60) {
      return `${durationMinutes} min`;
    } else {
      const hours = Math.floor(durationMinutes / 60);
      const minutes = durationMinutes % 60;
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
  };

  const getScheduleIcon = (schedule: EventSchedule) => {
    if (schedule.is_break) {
      return 'fas fa-coffee';
    } else if (schedule.title.toLowerCase().includes('keynote')) {
      return 'fas fa-microphone';
    } else if (schedule.title.toLowerCase().includes('panel')) {
      return 'fas fa-users';
    } else if (schedule.title.toLowerCase().includes('workshop')) {
      return 'fas fa-tools';
    } else if (schedule.title.toLowerCase().includes('lunch') || schedule.title.toLowerCase().includes('dinner')) {
      return 'fas fa-utensils';
    } else {
      return 'fas fa-presentation';
    }
  };

  const getScheduleColor = (schedule: EventSchedule) => {
    if (schedule.is_break) {
      return 'warning';
    } else if (schedule.title.toLowerCase().includes('keynote')) {
      return 'primary';
    } else if (schedule.title.toLowerCase().includes('panel')) {
      return 'info';
    } else if (schedule.title.toLowerCase().includes('workshop')) {
      return 'success';
    } else {
      return 'secondary';
    }
  };

  const groupedSchedules = groupSchedulesByDate(schedules);

  if (schedules.length === 0) {
    return (
      <div className="text-center py-5">
        <i className="fas fa-calendar-times text-muted" style={{ fontSize: '4rem' }}></i>
        <h4 className="mt-3 text-muted">No Schedule Available</h4>
        <p className="text-muted">The event schedule has not been published yet.</p>
      </div>
    );
  }

  return (
    <div>
      {Object.entries(groupedSchedules).map(([date, daySchedules]) => (
        <div key={date} className="mb-5">
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h3 className="text-primary fw-bold border-bottom border-primary pb-2">
              <i className="fas fa-calendar-day me-2"></i>
              {new Date(date).toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </h3>
            <Badge bg="primary" className="fs-6">
              {daySchedules.length} sessions
            </Badge>
          </div>
          
          <div className="timeline-container">
            {daySchedules.map((schedule, index) => (
              <div key={schedule.id} className="timeline-item mb-4">
                <Row className="align-items-center">
                  <Col md={2} className="text-center timeline-time">
                    <div className="time-block p-3 rounded bg-light border">
                      <div className="h5 text-primary mb-1 fw-bold">
                        {formatTime(schedule.start_time)}
                      </div>
                      <small className="text-muted d-block">
                        {formatTime(schedule.end_time)}
                      </small>
                      <Badge bg="secondary" className="small mt-2">
                        {formatDuration(schedule.start_time, schedule.end_time)}
                      </Badge>
                    </div>
                  </Col>
                  
                  <Col md={10}>
                    <Card className={`shadow-sm border-start border-${getScheduleColor(schedule)} border-4 schedule-card`}>
                      <Card.Body className="p-4">
                        <div className="d-flex align-items-start justify-content-between mb-3">
                          <div className="d-flex align-items-center">
                            <div className={`text-${getScheduleColor(schedule)} me-3`}>
                              <i className={`${getScheduleIcon(schedule)} fa-2x`}></i>
                            </div>
                            <div>
                              <h5 className="mb-1 fw-bold">{schedule.title}</h5>
                              {schedule.is_break && (
                                <Badge bg="warning" className="me-2">
                                  <i className="fas fa-coffee me-1"></i>
                                  Break
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        {schedule.description && (
                          <p className="text-muted mb-3 fs-6">{schedule.description}</p>
                        )}
                        
                        <div className="row g-3">
                          {schedule.location && (
                            <div className="col-md-6">
                              <div className="d-flex align-items-center text-muted">
                                <i className="fas fa-map-marker-alt me-2 text-primary"></i>
                                <span className="fw-medium">{schedule.location}</span>
                              </div>
                            </div>
                          )}
                          {schedule.speaker && (
                            <div className="col-md-6">
                              <div className="d-flex align-items-center text-muted">
                                <i className="fas fa-user me-2 text-primary"></i>
                                <span className="fw-medium">{schedule.speaker}</span>
                              </div>
                            </div>
                          )}
                        </div>
                        
                        {!schedule.is_break && (
                          <div className="mt-3 pt-3 border-top">
                            <div className="d-flex justify-content-between align-items-center">
                              <small className="text-muted">
                                <i className="fas fa-clock me-1"></i>
                                Duration: {formatDuration(schedule.start_time, schedule.end_time)}
                              </small>
                              <div className="d-flex gap-2">
                                <Badge bg="outline-primary" className="px-3 py-2">
                                  <i className="fas fa-calendar-check me-1"></i>
                                  Scheduled
                                </Badge>
                              </div>
                            </div>
                          </div>
                        )}
                      </Card.Body>
                    </Card>
                  </Col>
                </Row>
                
                {/* Timeline connector */}
                {index < daySchedules.length - 1 && (
                  <div className="timeline-connector">
                    <div className="connector-line"></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Custom Styles */}
      <style>{`
        .timeline-container {
          position: relative;
        }
        
        .timeline-item {
          position: relative;
        }
        
        .timeline-time {
          position: relative;
          z-index: 2;
        }
        
        .schedule-card {
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .schedule-card:hover {
          transform: translateX(5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
        }
        
        .timeline-connector {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width: 2px;
          height: 30px;
          z-index: 1;
        }
        
        .connector-line {
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom, #dee2e6, transparent);
        }
        
        @media (max-width: 768px) {
          .timeline-time {
            margin-bottom: 1rem;
          }
          
          .schedule-card:hover {
            transform: none;
          }
        }
        
        .time-block {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
          border: 2px solid #dee2e6 !important;
        }
        
        .border-primary {
          border-color: #0d6efd !important;
        }
        
        .border-warning {
          border-color: #ffc107 !important;
        }
        
        .border-info {
          border-color: #0dcaf0 !important;
        }
        
        .border-success {
          border-color: #198754 !important;
        }
        
        .border-secondary {
          border-color: #6c757d !important;
        }
      `}</style>
    </div>
  );
};

export default DailySchedule;
