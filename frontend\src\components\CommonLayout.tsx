import React from 'react';
import Landing<PERSON><PERSON>bar from './LandingNavbar';
import Footer from './Footer';

interface CommonLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showHero?: boolean;
  heroContent?: React.ReactNode;
  className?: string;
  contentInHero?: boolean;
}

const CommonLayout: React.FC<CommonLayoutProps> = ({
  children,
  title,
  subtitle,
  showHero = true,
  heroContent,
  className = '',
  contentInHero = false
}) => {
  return (
    <div className="d-flex flex-column bg-white" style={{ minHeight: '100vh' }}>
      {/* Navigation */}
      <LandingNavbar useHomeNavigation={true} />
      
      {/* Hero Section */}
      {showHero && (
        <div className="hero-section position-relative" style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          marginTop: '80px',
          minHeight: contentInHero ? 'calc(100vh - 80px)' : 'auto',
          paddingTop: contentInHero ? '40px' : '40px',
          paddingBottom: contentInHero ? '40px' : '40px'
        }}>
          <div className="container h-100">
            <div className="row h-100">
              <div className="col-lg-10 mx-auto text-center text-white">
                {heroContent ? (
                  heroContent
                ) : (
                  <>
                    {title && <h2 className="fw-bold mb-3">{title}</h2>}
                    {subtitle && <p className="mb-4" style={{ fontSize: '1.1rem' }}>{subtitle}</p>}
                  </>
                )}

                {/* Content inside hero with fancy styling */}
                {contentInHero && (
                  <div className="hero-content-wrapper position-relative" style={{ marginTop: '40px' }}>
                    <div
                      className="content-card bg-white rounded-4 shadow-lg p-4 mx-auto"
                      style={{
                        maxWidth: '1200px',
                        width: '100%',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'
                      }}
                    >
                      {children}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content - only show if content is not in hero */}
      {!contentInHero && (
        <main className={`flex-grow-1 bg-white ${className}`}>
          {children}
        </main>
      )}



      {/* Footer */}
      <Footer />
    </div>
  );
};

export default CommonLayout;
