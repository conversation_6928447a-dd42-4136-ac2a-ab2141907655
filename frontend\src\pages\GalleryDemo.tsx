import React, { useState } from 'react';
import { Container, Row, Col, Card, Nav, Tab } from 'react-bootstrap';
import Gallery from '../components/Gallery';
import { NewsGallery, AboutGallery, DepartmentGallery, CampusGallery } from '../components/SectionGallery';
import { EventGallery } from '../services/api';

// Mock data for demonstration
const mockEventImages: EventGallery[] = [
  {
    id: 1,
    title: "Conference Opening Ceremony",
    description: "The grand opening of the annual academic conference with distinguished guests.",
    image: "/media/gallery/conference-opening.jpg",
    uploaded_at: "2024-01-15T09:00:00Z",
    is_featured: true,
    event: 1
  },
  {
    id: 2,
    title: "Keynote Speaker Presentation",
    description: "<PERSON><PERSON> <PERSON> delivering the keynote address on modern education.",
    image: "/media/gallery/keynote-speaker.jpg",
    uploaded_at: "2024-01-15T10:30:00Z",
    is_featured: false,
    event: 1
  },
  {
    id: 3,
    title: "Student Panel Discussion",
    description: "Students engaging in a lively panel discussion about future careers.",
    image: "/media/gallery/student-panel.jpg",
    uploaded_at: "2024-01-16T14:00:00Z",
    is_featured: true,
    event: 1
  },
  {
    id: 4,
    title: "Networking Session",
    description: "Participants networking during the coffee break.",
    image: "/media/gallery/networking.jpg",
    uploaded_at: "2024-01-16T15:30:00Z",
    is_featured: false,
    event: 1
  }
];

const mockNewsImages: EventGallery[] = [
  {
    id: 5,
    title: "University Research Breakthrough",
    description: "Scientists at UoG make groundbreaking discovery in renewable energy.",
    image: "/media/gallery/research-lab.jpg",
    uploaded_at: "2024-01-10T12:00:00Z",
    is_featured: true,
    event: 2
  },
  {
    id: 6,
    title: "New Campus Building",
    description: "Construction of the new engineering building nears completion.",
    image: "/media/gallery/new-building.jpg",
    uploaded_at: "2024-01-12T16:00:00Z",
    is_featured: false,
    event: 2
  }
];

const mockCampusImages: EventGallery[] = [
  {
    id: 7,
    title: "Campus Library",
    description: "Students studying in the modern university library.",
    image: "/media/gallery/library.jpg",
    uploaded_at: "2024-01-05T11:00:00Z",
    is_featured: true,
    event: 3
  },
  {
    id: 8,
    title: "Sports Complex",
    description: "State-of-the-art sports facilities for student recreation.",
    image: "/media/gallery/sports-complex.jpg",
    uploaded_at: "2024-01-05T13:00:00Z",
    is_featured: false,
    event: 3
  }
];

const GalleryDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('events');

  return (
    <Container className="py-5">
      <Row className="mb-5">
        <Col>
          <div className="text-center">
            <h1 className="display-4 fw-bold mb-3">Gallery Component Demo</h1>
            <p className="lead text-muted">
              Explore different ways to use the reusable Gallery component across various sections
            </p>
          </div>
        </Col>
      </Row>

      <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'events')}>
        <Row>
          <Col>
            <Nav variant="pills" className="justify-content-center mb-4">
              <Nav.Item>
                <Nav.Link eventKey="events">Event Gallery</Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="news">News Gallery</Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="campus">Campus Gallery</Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="compact">Compact View</Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="portfolio">Portfolio Style</Nav.Link>
              </Nav.Item>
            </Nav>

            <Tab.Content>
              {/* Event Gallery Tab */}
              <Tab.Pane eventKey="events">
                <Card className="shadow-sm">
                  <Card.Header className="bg-primary text-white">
                    <h4 className="mb-0">
                      <i className="fas fa-calendar-alt me-2"></i>
                      Event Gallery - Date Grouped
                    </h4>
                  </Card.Header>
                  <Card.Body>
                    <p className="text-muted mb-4">
                      Perfect for event photos with date grouping and download functionality.
                    </p>
                    <Gallery
                      images={mockEventImages}
                      showDateGrouping={true}
                      showDownloadButton={true}
                      showFeaturedBadge={true}
                      gridColumns={{ lg: 3, md: 4, sm: 6 }}
                      downloadPrefix="conference"
                      emptyStateMessage="No event photos available"
                      emptyStateIcon="fas fa-camera"
                    />
                  </Card.Body>
                </Card>
              </Tab.Pane>

              {/* News Gallery Tab */}
              <Tab.Pane eventKey="news">
                <Card className="shadow-sm">
                  <Card.Header className="bg-info text-white">
                    <h4 className="mb-0">
                      <i className="fas fa-newspaper me-2"></i>
                      News Gallery - Simple Grid
                    </h4>
                  </Card.Header>
                  <Card.Body>
                    <p className="text-muted mb-4">
                      Clean layout for news articles without downloads, focusing on content.
                    </p>
                    <Gallery
                      images={mockNewsImages}
                      title="Latest News"
                      showDateGrouping={false}
                      showDownloadButton={false}
                      showFeaturedBadge={true}
                      gridColumns={{ lg: 4, md: 6, sm: 12 }}
                      imageHeight="220px"
                      downloadPrefix="news"
                      emptyStateMessage="No news images available"
                      emptyStateIcon="fas fa-newspaper"
                    />
                  </Card.Body>
                </Card>
              </Tab.Pane>

              {/* Campus Gallery Tab */}
              <Tab.Pane eventKey="campus">
                <Card className="shadow-sm">
                  <Card.Header className="bg-success text-white">
                    <h4 className="mb-0">
                      <i className="fas fa-university me-2"></i>
                      Campus Gallery - Standard Layout
                    </h4>
                  </Card.Header>
                  <Card.Body>
                    <p className="text-muted mb-4">
                      Showcase campus facilities and student life with a balanced layout.
                    </p>
                    <Gallery
                      images={mockCampusImages}
                      title="Campus Life"
                      showDateGrouping={false}
                      showDownloadButton={true}
                      showFeaturedBadge={true}
                      gridColumns={{ lg: 3, md: 4, sm: 6 }}
                      imageHeight="250px"
                      downloadPrefix="campus"
                      emptyStateMessage="No campus images available"
                      emptyStateIcon="fas fa-building"
                    />
                  </Card.Body>
                </Card>
              </Tab.Pane>

              {/* Compact Gallery Tab */}
              <Tab.Pane eventKey="compact">
                <Card className="shadow-sm">
                  <Card.Header className="bg-warning text-dark">
                    <h4 className="mb-0">
                      <i className="fas fa-th me-2"></i>
                      Compact Gallery - Sidebar Style
                    </h4>
                  </Card.Header>
                  <Card.Body>
                    <p className="text-muted mb-4">
                      Compact layout perfect for sidebars or smaller sections.
                    </p>
                    <Gallery
                      images={mockEventImages}
                      showDateGrouping={false}
                      showDownloadButton={false}
                      showFeaturedBadge={false}
                      gridColumns={{ lg: 6, md: 6, sm: 12 }}
                      imageHeight="120px"
                      emptyStateMessage="No thumbnails available"
                      className="compact-gallery"
                    />
                  </Card.Body>
                </Card>
              </Tab.Pane>

              {/* Portfolio Gallery Tab */}
              <Tab.Pane eventKey="portfolio">
                <Card className="shadow-sm">
                  <Card.Header className="bg-dark text-white">
                    <h4 className="mb-0">
                      <i className="fas fa-briefcase me-2"></i>
                      Portfolio Gallery - Large Images
                    </h4>
                  </Card.Header>
                  <Card.Body>
                    <p className="text-muted mb-4">
                      Large format gallery perfect for showcasing high-quality images.
                    </p>
                    <Gallery
                      images={mockCampusImages}
                      title="Featured Work"
                      showDateGrouping={false}
                      showDownloadButton={true}
                      showFeaturedBadge={true}
                      gridColumns={{ lg: 2, md: 3, sm: 6 }}
                      imageHeight="300px"
                      downloadPrefix="portfolio"
                      emptyStateMessage="No portfolio items"
                      emptyStateIcon="fas fa-briefcase"
                    />
                  </Card.Body>
                </Card>
              </Tab.Pane>
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      {/* Usage Instructions */}
      <Row className="mt-5">
        <Col>
          <Card className="bg-light">
            <Card.Body>
              <h5 className="mb-3">
                <i className="fas fa-code me-2"></i>
                Implementation Guide
              </h5>
              <p className="mb-3">
                The Gallery component is highly flexible and can be customized for any section:
              </p>
              <ul className="list-unstyled">
                <li className="mb-2">
                  <i className="fas fa-check text-success me-2"></i>
                  <strong>Event Photos:</strong> Use with date grouping and downloads
                </li>
                <li className="mb-2">
                  <i className="fas fa-check text-success me-2"></i>
                  <strong>News Articles:</strong> Simple grid without downloads
                </li>
                <li className="mb-2">
                  <i className="fas fa-check text-success me-2"></i>
                  <strong>Campus Showcase:</strong> Standard layout with featured badges
                </li>
                <li className="mb-2">
                  <i className="fas fa-check text-success me-2"></i>
                  <strong>Sidebar Content:</strong> Compact view with smaller images
                </li>
                <li className="mb-2">
                  <i className="fas fa-check text-success me-2"></i>
                  <strong>Portfolio:</strong> Large format for high-quality displays
                </li>
              </ul>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default GalleryDemo;
