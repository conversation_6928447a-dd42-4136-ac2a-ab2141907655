from rest_framework import serializers
from .models import Event, EventSchedule, EventGallery, EmailConfiguration, EmailTemplate, EmailLog, EventSponsor, EventOrganizer
from datetime import datetime, time
from django.utils import timezone


class EventScheduleSerializer(serializers.ModelSerializer):
    start_time = serializers.DateTimeField(format='%Y-%m-%dT%H:%M:%S')
    end_time = serializers.DateTimeField(format='%Y-%m-%dT%H:%M:%S')

    class Meta:
        model = EventSchedule
        fields = '__all__'

    def validate(self, data):
        """Custom validation for schedule data"""
        start_time = data.get('start_time')
        end_time = data.get('end_time')

        if start_time and end_time:
            if end_time <= start_time:
                raise serializers.ValidationError("End time must be after start time")

        return data

    def to_internal_value(self, data):
        """Convert frontend time strings to datetime objects"""
        # Handle the case where frontend sends time strings instead of full datetime
        if 'start_time' in data and isinstance(data['start_time'], str):
            if ':' in data['start_time'] and len(data['start_time']) <= 8:  # Time format like "10:00" or "10:00:00"
                # Get the event to determine the date
                event_id = data.get('event')
                if event_id:
                    try:
                        event = Event.objects.get(id=event_id)
                        # Use the event start date as the base date
                        base_date = event.start_date.date()
                        time_obj = datetime.strptime(data['start_time'], '%H:%M' if len(data['start_time']) == 5 else '%H:%M:%S').time()
                        data['start_time'] = timezone.make_aware(datetime.combine(base_date, time_obj))
                    except (Event.DoesNotExist, ValueError):
                        pass

        if 'end_time' in data and isinstance(data['end_time'], str):
            if ':' in data['end_time'] and len(data['end_time']) <= 8:  # Time format like "18:00" or "18:00:00"
                # Get the event to determine the date
                event_id = data.get('event')
                if event_id:
                    try:
                        event = Event.objects.get(id=event_id)
                        # Use the event start date as the base date
                        base_date = event.start_date.date()
                        time_obj = datetime.strptime(data['end_time'], '%H:%M' if len(data['end_time']) == 5 else '%H:%M:%S').time()
                        data['end_time'] = timezone.make_aware(datetime.combine(base_date, time_obj))
                    except (Event.DoesNotExist, ValueError):
                        pass

        return super().to_internal_value(data)

    def to_representation(self, instance):
        """Convert datetime objects to frontend-friendly format"""
        data = super().to_representation(instance)

        # Convert datetime to time string for frontend compatibility
        if instance.start_time:
            data['start_time'] = instance.start_time.strftime('%H:%M')
        if instance.end_time:
            data['end_time'] = instance.end_time.strftime('%H:%M')

        return data


class EventGallerySerializer(serializers.ModelSerializer):
    class Meta:
        model = EventGallery
        fields = '__all__'


class EventSponsorSerializer(serializers.ModelSerializer):
    class Meta:
        model = EventSponsor
        fields = ['id', 'name', 'logo', 'website', 'description', 'sponsor_type', 'display_order', 'is_active']


class EventOrganizerSerializer(serializers.ModelSerializer):
    class Meta:
        model = EventOrganizer
        fields = ['id', 'name', 'title', 'photo', 'email', 'phone', 'bio', 'is_primary', 'display_order', 'is_active']


class EventSerializer(serializers.ModelSerializer):
    schedules = EventScheduleSerializer(many=True, read_only=True)
    gallery = EventGallerySerializer(many=True, read_only=True)
    sponsors = EventSponsorSerializer(many=True, read_only=True)
    organizers = EventOrganizerSerializer(many=True, read_only=True)
    participant_count = serializers.SerializerMethodField()

    class Meta:
        model = Event
        fields = '__all__'

    def get_participant_count(self, obj):
        return obj.participants.count()


class EventListSerializer(serializers.ModelSerializer):
    """Simplified serializer for event list views"""
    participant_count = serializers.SerializerMethodField()

    class Meta:
        model = Event
        fields = ['id', 'name', 'description', 'start_date', 'end_date', 
                 'location', 'city', 'country', 'logo', 'banner', 
                 'is_active', 'participant_count']

    def get_participant_count(self, obj):
        return obj.participants.count()


class EmailConfigurationSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmailConfiguration
        fields = '__all__'
        extra_kwargs = {
            'email_host_password': {'write_only': True}
        }


class EmailTemplateSerializer(serializers.ModelSerializer):
    template_type_display = serializers.CharField(source='get_template_type_display', read_only=True)

    class Meta:
        model = EmailTemplate
        fields = '__all__'


class EmailLogSerializer(serializers.ModelSerializer):
    template_type_display = serializers.CharField(source='get_template_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = EmailLog
        fields = '__all__'
