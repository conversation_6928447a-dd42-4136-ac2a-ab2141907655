#!/usr/bin/env python3
"""
Test script to verify badge generation matches the SVG template exactly
"""
import os
import sys
from PIL import Image, ImageDraw, ImageFont
import qrcode

# Mock participant class for testing
class MockParticipant:
    def __init__(self):
        self.first_name = "ELIZABETH"
        self.last_name = "G<PERSON>MORE"
        self.institution_name = "University of Gondar"
        self.position = "Faculty of Advanced Sciences"
        self.participant_type = MockParticipantType()
        self.event = MockEvent()

class MockParticipantType:
    def __init__(self):
        self.name = "KEYNOTE SPEAKER"

class MockEvent:
    def __init__(self):
        self.name = "GLOBAL ACADEMIC SUMMIT"
        self.start_date = "OCTOBER 15-17, 2025"

def generate_test_badge():
    """Generate a test badge matching the SVG template exactly"""
    # Badge dimensions (90mm x 140mm at 300 DPI)
    width = int(90 * 11.81)   # ~1063 pixels
    height = int(140 * 11.81) # ~1653 pixels
    
    # Create image with light background
    img = Image.new('RGB', (width, height), '#f8fafc')
    draw = ImageDraw.Draw(img)
    
    # Add banner ending at photo midpoint (35mm) with fade
    banner_height = int(35 * 11.81)  # ~413 pixels
    for y in range(banner_height):
        progress = y / banner_height
        if progress <= 0.5:
            # Gradient from #1e3a8a to #3b82f6 (first 50%)
            r = int(30 + (59 - 30) * (progress * 2))
            g = int(58 + (130 - 58) * (progress * 2))
            b = int(138 + (246 - 138) * (progress * 2))
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        else:
            # Complete fade out after 50%
            break
    
    # Event header text
    try:
        header_font = ImageFont.truetype("arial.ttf", 57)  # 4.8mm equivalent
        date_font = ImageFont.truetype("arial.ttf", 36)    # 3mm equivalent
    except:
        header_font = ImageFont.load_default()
        date_font = ImageFont.load_default()
    
    center_x = width // 2
    draw.text((center_x, int(12 * 11.81)), "GLOBAL ACADEMIC SUMMIT", 
             font=header_font, fill='white', anchor='mt')
    draw.text((center_x, int(17 * 11.81)), "OCTOBER 15-17, 2025", 
             font=date_font, fill='#bfdbfe', anchor='mt')
    
    # Photo with dashed circle frame (center at x=45mm, y=35mm)
    photo_center_x = int(45 * 11.81)  # ~531 pixels
    photo_center_y = int(35 * 11.81)  # ~413 pixels
    photo_radius = int(16 * 11.81)    # 16mm radius
    inner_radius = int(14 * 11.81)    # 14mm inner radius
    
    # White outer circle
    draw.ellipse([photo_center_x - photo_radius, photo_center_y - photo_radius,
                  photo_center_x + photo_radius, photo_center_y + photo_radius],
                 fill='white', outline='#d4af37', width=int(1.5 * 11.81))
    
    # Inner circle with placeholder
    draw.ellipse([photo_center_x - inner_radius, photo_center_y - inner_radius,
                  photo_center_x + inner_radius, photo_center_y + inner_radius],
                 fill='#f0f9ff')
    
    try:
        placeholder_font = ImageFont.truetype("arial.ttf", 21)
    except:
        placeholder_font = ImageFont.load_default()
    
    draw.text((photo_center_x, photo_center_y), "PORTRAIT", 
             font=placeholder_font, fill='#93c5fd', anchor='mm')
    
    # Participant information
    try:
        name_font = ImageFont.truetype("times.ttf", 69)      # 5.8mm equivalent
        institution_font = ImageFont.truetype("times.ttf", 45)  # 3.8mm equivalent
        position_font = ImageFont.truetype("times.ttf", 38)     # 3.2mm equivalent
    except:
        name_font = ImageFont.load_default()
        institution_font = ImageFont.load_default()
        position_font = ImageFont.load_default()
    
    # Name (y=58mm)
    draw.text((center_x, int(58 * 11.81)), "ELIZABETH GILMORE", 
             font=name_font, fill='#1e3a8a', anchor='mt')
    
    # Institution (y=66mm)
    draw.text((center_x, int(66 * 11.81)), "University of Gondar", 
             font=institution_font, fill='#1e40af', anchor='mt')
    
    # Position (y=71mm)
    draw.text((center_x, int(71 * 11.81)), "Faculty of Advanced Sciences", 
             font=position_font, fill='#60a5fa', anchor='mt')
    
    # QR Code (x=30mm, y=80mm, 30x30mm)
    qr_x = int(30 * 11.81)
    qr_y = int(80 * 11.81)
    qr_size = int(30 * 11.81)
    
    # White background with gold border
    draw.rectangle([qr_x, qr_y, qr_x + qr_size, qr_y + qr_size],
                  fill='white', outline='#d4af37', width=int(1 * 11.81))
    
    # QR code placeholder
    qr_inner_size = qr_size - int(4 * 11.81)
    qr_inner_x = qr_x + int(2 * 11.81)
    qr_inner_y = qr_y + int(2 * 11.81)
    
    # Simple QR pattern simulation
    for i in range(0, qr_inner_size, 8):
        for j in range(0, qr_inner_size, 8):
            if (i + j) % 16 == 0:
                draw.rectangle([qr_inner_x + i, qr_inner_y + j, 
                               qr_inner_x + i + 6, qr_inner_y + j + 6], fill='black')
    
    # "Scan for profile" text
    try:
        scan_font = ImageFont.truetype("arial.ttf", 24)
    except:
        scan_font = ImageFont.load_default()
    
    draw.text((center_x, int(113 * 11.81)), "Scan for profile", 
             font=scan_font, fill='#3b82f6', anchor='mt')
    
    # Keynote Speaker Badge (x=20mm, y=118mm, 50x8mm)
    badge_x = int(20 * 11.81)
    badge_y = int(118 * 11.81)
    badge_width = int(50 * 11.81)
    badge_height = int(8 * 11.81)
    
    # Gold gradient background
    for y in range(badge_height):
        progress = y / badge_height
        r = int(212 + (253 - 212) * progress)  # #d4af37 to #fde047
        g = int(175 + (224 - 175) * progress)
        b = int(55 + (71 - 55) * progress)
        draw.line([(badge_x, badge_y + y), (badge_x + badge_width, badge_y + y)], fill=(r, g, b))
    
    # Badge text
    try:
        badge_font = ImageFont.truetype("times.ttf", 50)
    except:
        badge_font = ImageFont.load_default()
    
    text_center_x = badge_x + badge_width // 2
    text_center_y = badge_y + badge_height // 2
    draw.text((text_center_x, text_center_y), "KEYNOTE SPEAKER", 
             font=badge_font, fill='#1e3a8a', anchor='mm')
    
    # Sponsor placeholders (y=132mm)
    sponsor_y = int(132 * 11.81)
    sponsor_width = int(20 * 11.81)
    sponsor_height = int(10 * 11.81)
    
    for i, x_pos in enumerate([12, 35, 58]):
        sponsor_x = int(x_pos * 11.81)
        draw.rectangle([sponsor_x, sponsor_y, sponsor_x + sponsor_width, sponsor_y + sponsor_height],
                      fill='#e0f2fe', outline='#1e3a8a', width=int(0.3 * 11.81))
    
    # Save the test badge
    output_path = "test_badge_svg_match.png"
    img.save(output_path, "PNG", dpi=(300, 300))
    print(f"Test badge saved as: {output_path}")
    print("Please compare this with your SVG template to verify exact match!")
    
    return output_path

if __name__ == "__main__":
    generate_test_badge()
