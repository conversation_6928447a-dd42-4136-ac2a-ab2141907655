# Generated by Django 4.2.7 on 2025-07-27 15:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contact_persons', '0001_initial'),
        ('drivers', '0001_initial'),
        ('badges', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DriverBadge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qr_code_data', models.TextField()),
                ('qr_code_image', models.ImageField(blank=True, null=True, upload_to='qr_codes/')),
                ('badge_image', models.ImageField(blank=True, null=True, upload_to='generated_badges/')),
                ('is_generated', models.BooleanField(default=False)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('driver', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='badge', to='drivers.driver')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='badges.badgetemplate')),
            ],
        ),
        migrations.CreateModel(
            name='ContactPersonBadge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qr_code_data', models.TextField()),
                ('qr_code_image', models.ImageField(blank=True, null=True, upload_to='qr_codes/')),
                ('badge_image', models.ImageField(blank=True, null=True, upload_to='generated_badges/')),
                ('is_generated', models.BooleanField(default=False)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('contact_person', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='badge', to='contact_persons.contactperson')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='badges.badgetemplate')),
            ],
        ),
    ]
