import React, { useState } from 'react';
import { Navbar as Boots<PERSON><PERSON><PERSON><PERSON><PERSON>, Nav, Container, NavDropdown, <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { LinkContainer } from 'react-router-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useBranding } from '../hooks/useBranding';

const Navbar: React.FC = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const [loggingOut, setLoggingOut] = useState(false);
  const { organization } = useBranding();

  // Don't render navbar for non-authenticated users
  if (!isAuthenticated) {
    return null;
  }

  const handleLogout = async () => {
    setLoggingOut(true);
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoggingOut(false);
    }
  };

  return (
    <BootstrapNavbar bg="primary" variant="dark" expand="lg" className="mb-4">
      <Container>
        <LinkContainer to="/">
          <BootstrapNavbar.Brand className="d-flex align-items-center">
            {organization?.logo ? (
              <img
                src={organization.logo}
                alt={organization.name}
                className="me-2"
                style={{
                  height: '35px',
                  width: 'auto',
                  objectFit: 'contain'
                }}
              />
            ) : (
              <i className="fas fa-university me-2"></i>
            )}
            <div className="d-flex flex-column">
              <span style={{ fontSize: '1.1rem', lineHeight: '1.2' }}>
                {organization?.short_name || 'UoG'} Events
              </span>
              <small style={{ fontSize: '0.7rem', lineHeight: '1', opacity: 0.8 }}>
                {organization?.name || 'University of Gondar'}
              </small>
            </div>
          </BootstrapNavbar.Brand>
        </LinkContainer>

        <BootstrapNavbar.Toggle aria-controls="basic-navbar-nav" />
        <BootstrapNavbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">

            {user?.role_name === 'administrator' && (
              <NavDropdown
                title={
                  <>
                    <i className="fas fa-calendar me-1"></i>
                    Events
                  </>
                }
                id="events-dropdown"
              >
                <LinkContainer to="/events">
                  <NavDropdown.Item>
                    <i className="fas fa-list me-2"></i>
                    All Events
                  </NavDropdown.Item>
                </LinkContainer>
                <LinkContainer to="/events/new">
                  <NavDropdown.Item>
                    <i className="fas fa-plus me-2"></i>
                    Create Event
                  </NavDropdown.Item>
                </LinkContainer>
                <LinkContainer to="/schedule-management">
                  <NavDropdown.Item>
                    <i className="fas fa-clock me-2"></i>
                    Schedule Management
                  </NavDropdown.Item>
                </LinkContainer>
                <NavDropdown.Divider />
                <LinkContainer to="/participants/manage">
                  <NavDropdown.Item>
                    <i className="fas fa-users me-2"></i>
                    Manage Participants
                  </NavDropdown.Item>
                </LinkContainer>
              </NavDropdown>
            )}

            {user?.role_name === 'administrator' && (
              <LinkContainer to="/organizations">
                <Nav.Link>
                  <i className="fas fa-building me-1"></i>
                  Organizations
                </Nav.Link>
              </LinkContainer>
            )}



            <LinkContainer to="/participants/manage">
              <Nav.Link>
                <i className="fas fa-users-cog me-1"></i>
                Manage Participants
              </Nav.Link>
            </LinkContainer>

            {user?.role_name === 'administrator' && (
              <LinkContainer to="/scan">
                <Nav.Link>
                  <i className="fas fa-qrcode me-1"></i>
                  QR Scanner
                </Nav.Link>
              </LinkContainer>
            )}

            {/* Management Dropdown */}
            {user?.role_name === 'administrator' && (
              <NavDropdown title={
                <span>
                  <i className="fas fa-cogs me-1"></i>
                  Management
                </span>
              } id="management-dropdown">
                <LinkContainer to="/hotels">
                  <NavDropdown.Item>
                    <i className="fas fa-hotel me-2"></i>
                    Hotels
                  </NavDropdown.Item>
                </LinkContainer>
                <LinkContainer to="/drivers">
                  <NavDropdown.Item>
                    <i className="fas fa-car me-2"></i>
                    Drivers
                  </NavDropdown.Item>
                </LinkContainer>
                <LinkContainer to="/participant-types">
                  <NavDropdown.Item>
                    <i className="fas fa-tags me-2"></i>
                    Participant Types
                  </NavDropdown.Item>
                </LinkContainer>
                <LinkContainer to="/contact-persons">
                  <NavDropdown.Item>
                    <i className="fas fa-address-book me-2"></i>
                    Contact Persons
                  </NavDropdown.Item>
                </LinkContainer>
                <NavDropdown.Divider />
                <LinkContainer to="/email-management">
                  <NavDropdown.Item>
                    <i className="fas fa-envelope me-2"></i>
                    Email Management
                  </NavDropdown.Item>
                </LinkContainer>
                <LinkContainer to="/gallery-management">
                  <NavDropdown.Item>
                    <i className="fas fa-images me-2"></i>
                    Gallery Management
                  </NavDropdown.Item>
                </LinkContainer>
                <LinkContainer to="/attendance-management">
                  <NavDropdown.Item>
                    <i className="fas fa-qrcode me-2"></i>
                    Attendance Management
                  </NavDropdown.Item>
                </LinkContainer>
                <NavDropdown.Divider />
                <LinkContainer to="/badge-preview">
                  <NavDropdown.Item>
                    <i className="fas fa-id-badge me-2"></i>
                    Enhanced Badges
                  </NavDropdown.Item>
                </LinkContainer>
              </NavDropdown>
            )}

            {user?.role_name === 'administrator' && (
              <LinkContainer to="/admin">
                <Nav.Link>
                  <i className="fas fa-users-cog me-1"></i>
                  Admin
                </Nav.Link>
              </LinkContainer>
            )}

            {!isAuthenticated && (
              <LinkContainer to="/register">
                <Nav.Link>
                  <i className="fas fa-user-plus me-1"></i>
                  Register
                </Nav.Link>
              </LinkContainer>
            )}
          </Nav>

          <Nav>
            {isAuthenticated ? (
              <NavDropdown
                title={
                  <span>
                    <i className="fas fa-user-circle me-2"></i>
                    {user?.first_name || user?.username}
                    <Badge
                      bg="light"
                      text="dark"
                      className="ms-2"
                      style={{ backgroundColor: user?.role_color }}
                    >
                      {user?.role_display_name}
                    </Badge>
                  </span>
                }
                id="user-dropdown"
                align="end"
              >

                <NavDropdown.Item onClick={() => navigate('/profile')}>
                  <i className="fas fa-user-edit me-2"></i>
                  Profile
                </NavDropdown.Item>
                <NavDropdown.Divider />
                <NavDropdown.Item onClick={handleLogout} disabled={loggingOut}>
                  <i className="fas fa-sign-out-alt me-2"></i>
                  {loggingOut ? 'Signing Out...' : 'Sign Out'}
                </NavDropdown.Item>
              </NavDropdown>
            ) : (
              <LinkContainer to="/login">
                <Button variant="outline-light">
                  <i className="fas fa-sign-in-alt me-2"></i>
                  Sign In
                </Button>
              </LinkContainer>
            )}
          </Nav>
        </BootstrapNavbar.Collapse>
      </Container>
    </BootstrapNavbar>
  );
};

export default Navbar;
