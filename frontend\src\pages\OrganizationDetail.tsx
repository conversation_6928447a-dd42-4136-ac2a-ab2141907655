import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Container, <PERSON>, <PERSON>, <PERSON>, Badge, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, Tab, Nav } from 'react-bootstrap';
import { organizationService, Organization, OrganizationSettings, getMediaUrl } from '../services/api';

const OrganizationDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [settings, setSettings] = useState<OrganizationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (id) {
      fetchOrganization();
      fetchSettings();
    }
  }, [id]);

  const fetchOrganization = async () => {
    try {
      setLoading(true);
      const response = await organizationService.getOrganization(parseInt(id!));
      setOrganization(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching organization:', err);
      setError('Failed to load organization details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await organizationService.getSettings(parseInt(id!));
      setSettings(response.data);
    } catch (err) {
      console.error('Error fetching organization settings:', err);
      // Settings might not exist, which is okay
    }
  };

  const handleSetPrimary = async () => {
    if (!organization) return;
    
    try {
      await organizationService.setPrimaryOrganization(organization.id);
      await fetchOrganization(); // Refresh data
      setError(null);
    } catch (err) {
      console.error('Error setting primary organization:', err);
      setError('Failed to set as primary organization. Please try again.');
    }
  };

  const handleToggleActive = async () => {
    if (!organization) return;
    
    try {
      await organizationService.toggleActive(organization.id);
      await fetchOrganization(); // Refresh data
      setError(null);
    } catch (err) {
      console.error('Error toggling organization status:', err);
      setError('Failed to update organization status. Please try again.');
    }
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading organization details...</p>
      </Container>
    );
  }

  if (!organization) {
    return (
      <Container className="mt-5">
        <Alert variant="danger">
          <h4>Organization Not Found</h4>
          <p>The organization you're looking for doesn't exist or has been removed.</p>
          <Link to="/organizations" className="btn btn-primary">
            <i className="fas fa-arrow-left me-2"></i>
            Back to Organizations
          </Link>
        </Alert>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      {error && (
        <Alert variant="danger" className="mb-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {/* Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-start">
            <div className="d-flex align-items-center">
              {organization.logo && (
                <img 
                  src={getMediaUrl(organization.logo)} 
                  alt={organization.name}
                  className="rounded me-4"
                  style={{ width: '80px', height: '80px', objectFit: 'cover' }}
                />
              )}
              <div>
                <h1 className="display-6 fw-bold text-primary mb-1">
                  {organization.name}
                  {organization.short_name && (
                    <small className="text-muted ms-2">({organization.short_name})</small>
                  )}
                </h1>
                {organization.motto && (
                  <p className="lead text-muted fst-italic mb-2">"{organization.motto}"</p>
                )}
                <div className="d-flex gap-2">
                  <Badge bg={organization.is_active ? 'success' : 'secondary'}>
                    {organization.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                  {organization.is_primary && (
                    <Badge bg="warning" text="dark">
                      <i className="fas fa-star me-1"></i>
                      Primary Organization
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="d-flex gap-2">
              <Link to={`/organizations/${organization.id}/edit`} className="btn btn-outline-primary">
                <i className="fas fa-edit me-2"></i>
                Edit
              </Link>
              {!organization.is_primary && (
                <Button variant="outline-warning" onClick={handleSetPrimary}>
                  <i className="fas fa-star me-2"></i>
                  Set as Primary
                </Button>
              )}
              <Button 
                variant={organization.is_active ? 'outline-secondary' : 'outline-success'}
                onClick={handleToggleActive}
              >
                <i className={`fas fa-${organization.is_active ? 'pause' : 'play'} me-2`}></i>
                {organization.is_active ? 'Deactivate' : 'Activate'}
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {/* Tabs */}
      <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'overview')}>
        <Nav variant="tabs" className="mb-4">
          <Nav.Item>
            <Nav.Link eventKey="overview">
              <i className="fas fa-info-circle me-2"></i>
              Overview
            </Nav.Link>
          </Nav.Item>
          <Nav.Item>
            <Nav.Link eventKey="contact">
              <i className="fas fa-address-book me-2"></i>
              Contact & Address
            </Nav.Link>
          </Nav.Item>
          <Nav.Item>
            <Nav.Link eventKey="social">
              <i className="fas fa-share-alt me-2"></i>
              Social Media
            </Nav.Link>
          </Nav.Item>
          {settings && (
            <Nav.Item>
              <Nav.Link eventKey="settings">
                <i className="fas fa-cogs me-2"></i>
                Settings
              </Nav.Link>
            </Nav.Item>
          )}
        </Nav>

        <Tab.Content>
          {/* Overview Tab */}
          <Tab.Pane eventKey="overview">
            <Row>
              <Col lg={8}>
                <Card className="mb-4">
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-info-circle me-2"></i>
                      Organization Information
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    {organization.description ? (
                      <p className="mb-0">{organization.description}</p>
                    ) : (
                      <p className="text-muted mb-0">No description available.</p>
                    )}
                  </Card.Body>
                </Card>
              </Col>
              <Col lg={4}>
                <Card>
                  <Card.Header>
                    <h6 className="mb-0">
                      <i className="fas fa-chart-bar me-2"></i>
                      Quick Stats
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <span>Status:</span>
                      <Badge bg={organization.is_active ? 'success' : 'secondary'}>
                        {organization.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <span>Type:</span>
                      <Badge bg={organization.is_primary ? 'warning' : 'info'} text={organization.is_primary ? 'dark' : 'white'}>
                        {organization.is_primary ? 'Primary' : 'Secondary'}
                      </Badge>
                    </div>
                    <div className="d-flex justify-content-between align-items-center">
                      <span>Created:</span>
                      <small className="text-muted">
                        {new Date(organization.created_at).toLocaleDateString()}
                      </small>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </Tab.Pane>

          {/* Contact Tab */}
          <Tab.Pane eventKey="contact">
            <Row>
              <Col md={6}>
                <Card className="mb-4">
                  <Card.Header>
                    <h6 className="mb-0">
                      <i className="fas fa-envelope me-2"></i>
                      Contact Information
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    <div className="mb-3">
                      <strong>Email:</strong><br />
                      <a href={`mailto:${organization.email}`} className="text-decoration-none">
                        {organization.email}
                      </a>
                    </div>
                    {organization.phone && (
                      <div className="mb-3">
                        <strong>Phone:</strong><br />
                        <a href={`tel:${organization.phone}`} className="text-decoration-none">
                          {organization.phone}
                        </a>
                      </div>
                    )}
                    {organization.website && (
                      <div className="mb-0">
                        <strong>Website:</strong><br />
                        <a href={organization.website} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                          {organization.website}
                        </a>
                      </div>
                    )}
                  </Card.Body>
                </Card>
              </Col>
              <Col md={6}>
                <Card>
                  <Card.Header>
                    <h6 className="mb-0">
                      <i className="fas fa-map-marker-alt me-2"></i>
                      Address
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    {organization.full_address ? (
                      <address className="mb-0">
                        {organization.address_line_1 && <>{organization.address_line_1}<br /></>}
                        {organization.address_line_2 && <>{organization.address_line_2}<br /></>}
                        {organization.city && organization.state_province && (
                          <>{organization.city}, {organization.state_province}<br /></>
                        )}
                        {organization.postal_code && <>{organization.postal_code}<br /></>}
                        {organization.country && <>{organization.country}</>}
                      </address>
                    ) : (
                      <p className="text-muted mb-0">No address information available.</p>
                    )}
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </Tab.Pane>

          {/* Social Media Tab */}
          <Tab.Pane eventKey="social">
            <Card>
              <Card.Header>
                <h6 className="mb-0">
                  <i className="fas fa-share-alt me-2"></i>
                  Social Media Profiles
                </h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  {organization.facebook_url && (
                    <Col md={6} className="mb-3">
                      <div className="d-flex align-items-center">
                        <i className="fab fa-facebook fa-2x text-primary me-3"></i>
                        <div>
                          <strong>Facebook</strong><br />
                          <a href={organization.facebook_url} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                            View Profile
                          </a>
                        </div>
                      </div>
                    </Col>
                  )}
                  {organization.twitter_url && (
                    <Col md={6} className="mb-3">
                      <div className="d-flex align-items-center">
                        <i className="fab fa-twitter fa-2x text-info me-3"></i>
                        <div>
                          <strong>Twitter</strong><br />
                          <a href={organization.twitter_url} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                            View Profile
                          </a>
                        </div>
                      </div>
                    </Col>
                  )}
                  {organization.linkedin_url && (
                    <Col md={6} className="mb-3">
                      <div className="d-flex align-items-center">
                        <i className="fab fa-linkedin fa-2x text-primary me-3"></i>
                        <div>
                          <strong>LinkedIn</strong><br />
                          <a href={organization.linkedin_url} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                            View Profile
                          </a>
                        </div>
                      </div>
                    </Col>
                  )}
                  {organization.instagram_url && (
                    <Col md={6} className="mb-3">
                      <div className="d-flex align-items-center">
                        <i className="fab fa-instagram fa-2x text-danger me-3"></i>
                        <div>
                          <strong>Instagram</strong><br />
                          <a href={organization.instagram_url} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                            View Profile
                          </a>
                        </div>
                      </div>
                    </Col>
                  )}
                </Row>
                {!organization.facebook_url && !organization.twitter_url && !organization.linkedin_url && !organization.instagram_url && (
                  <p className="text-muted mb-0">No social media profiles available.</p>
                )}
              </Card.Body>
            </Card>
          </Tab.Pane>

          {/* Settings Tab */}
          {settings && (
            <Tab.Pane eventKey="settings">
              <Card>
                <Card.Header>
                  <h6 className="mb-0">
                    <i className="fas fa-cogs me-2"></i>
                    Organization Settings
                  </h6>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <h6 className="text-primary">Event Settings</h6>
                      <p><strong>Default Event Duration:</strong> {settings.default_event_duration_hours} hours</p>
                      <p><strong>Default Registration Fee:</strong> {settings.default_registration_fee} ETB</p>
                    </Col>
                    <Col md={6}>
                      <h6 className="text-primary">Branding</h6>
                      <p><strong>Primary Color:</strong> 
                        <span 
                          className="ms-2 px-3 py-1 rounded text-white"
                          style={{ backgroundColor: settings.primary_color }}
                        >
                          {settings.primary_color}
                        </span>
                      </p>
                      <p><strong>Secondary Color:</strong> 
                        <span 
                          className="ms-2 px-3 py-1 rounded text-white"
                          style={{ backgroundColor: settings.secondary_color }}
                        >
                          {settings.secondary_color}
                        </span>
                      </p>
                    </Col>
                  </Row>
                  <hr />
                  <h6 className="text-primary">Notification Settings</h6>
                  <Row>
                    <Col md={4}>
                      <p><strong>Welcome Emails:</strong> {settings.send_welcome_emails ? 'Enabled' : 'Disabled'}</p>
                    </Col>
                    <Col md={4}>
                      <p><strong>Confirmation Emails:</strong> {settings.send_confirmation_emails ? 'Enabled' : 'Disabled'}</p>
                    </Col>
                    <Col md={4}>
                      <p><strong>Reminder Emails:</strong> {settings.send_reminder_emails ? 'Enabled' : 'Disabled'}</p>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </Tab.Pane>
          )}
        </Tab.Content>
      </Tab.Container>

      {/* Back Button */}
      <div className="mt-4">
        <Link to="/organizations" className="btn btn-secondary">
          <i className="fas fa-arrow-left me-2"></i>
          Back to Organizations
        </Link>
      </div>
    </Container>
  );
};

export default OrganizationDetail;
