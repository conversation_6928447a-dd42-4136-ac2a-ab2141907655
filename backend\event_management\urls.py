"""
URL configuration for event_management project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from django.shortcuts import get_object_or_404


def health_check(request):
    """Health check endpoint"""
    return JsonResponse({
        'status': 'healthy',
        'service': 'UoG Event Management System',
        'version': '1.0.0'
    })


def verify_participant(request, uuid):
    """Verify participant by UUID for QR code scanning"""
    from participants.models import Participant
    from participants.serializers import ParticipantListSerializer

    try:
        participant = get_object_or_404(Participant, uuid=uuid)
        serializer = ParticipantListSerializer(participant)
        return JsonResponse({
            'success': True,
            'participant': serializer.data
        })
    except:
        return JsonResponse({
            'success': False,
            'error': 'Participant not found'
        }, status=404)


urlpatterns = [
    path("admin/", admin.site.urls),
    path("health/", health_check, name="health_check"),
    path("api/auth/", include("authentication.urls")),
    path("api/", include("events.urls")),
    path("api/", include("participants.urls")),
    path("api/", include("drivers.urls")),
    path("api/", include("hotels.urls")),
    path("api/", include("contact_persons.urls")),
    path("api/", include("badges.urls")),
    path("api/", include("organizations.urls")),
    path("verify/<uuid:uuid>/", verify_participant, name="verify_participant"),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
