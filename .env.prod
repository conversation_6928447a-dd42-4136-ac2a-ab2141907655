# Production Environment Configuration
# Copy this file to .env and update the values for your production environment

# D<PERSON><PERSON> Settings
SECRET_KEY=your-super-secret-production-key-change-this-immediately
DEBUG=False

# Database Configuration
POSTGRES_DB=uog_event_prod
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-database-password

# Server Configuration
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,backend,nginx,************,your-domain.com
CORS_ALLOWED_ORIGINS=http://localhost,http://127.0.0.1,http://************,https://************,https://your-domain.com

# URLs
QR_CODE_BASE_URL=http://************
REACT_APP_API_BASE_URL=http://************/api
REACT_APP_BACKEND_URL=http://************

# Email Configuration (Update with your SMTP settings)
EMAIL_HOST=smtp.office365.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=UoG Events <<EMAIL>>

# Security (Optional - for enhanced security)
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS=True
# SECURE_HSTS_PRELOAD=True
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True
