from django.core.management.base import BaseCommand
from badges.models import Badge, BadgeTemplate
from participants.models import Participant
from events.models import Event


class Command(BaseCommand):
    help = 'Generate sample badges for testing the new professional design'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--event-id',
            type=int,
            help='Event ID to generate badges for (optional)',
        )
        parser.add_argument(
            '--participant-id',
            type=int,
            help='Specific participant ID to generate badge for (optional)',
        )
        parser.add_argument(
            '--template-id',
            type=int,
            help='Specific template ID to use (optional)',
        )
        parser.add_argument(
            '--regenerate',
            action='store_true',
            help='Regenerate existing badges',
        )
    
    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🎨 Professional Badge Generation System')
        )
        self.stdout.write('=' * 50)
        
        # Get template
        template = None
        if options['template_id']:
            try:
                template = BadgeTemplate.objects.get(id=options['template_id'])
                self.stdout.write(f'📋 Using template: {template.name}')
            except BadgeTemplate.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'❌ Template with ID {options["template_id"]} not found')
                )
                return
        else:
            template = BadgeTemplate.objects.filter(is_default=True).first()
            if template:
                self.stdout.write(f'📋 Using default template: {template.name}')
            else:
                self.stdout.write(
                    self.style.WARNING('⚠️  No default template found, using system defaults')
                )
        
        # Get participants
        participants = []
        
        if options['participant_id']:
            try:
                participant = Participant.objects.get(id=options['participant_id'])
                participants = [participant]
                self.stdout.write(f'👤 Generating badge for: {participant.full_name}')
            except Participant.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'❌ Participant with ID {options["participant_id"]} not found')
                )
                return
        elif options['event_id']:
            try:
                event = Event.objects.get(id=options['event_id'])
                participants = Participant.objects.filter(event=event)
                self.stdout.write(f'🎪 Generating badges for event: {event.name}')
                self.stdout.write(f'👥 Found {participants.count()} participants')
            except Event.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'❌ Event with ID {options["event_id"]} not found')
                )
                return
        else:
            # Get all participants
            participants = Participant.objects.all()[:10]  # Limit to 10 for testing
            self.stdout.write(f'👥 Generating badges for first 10 participants')
        
        if not participants:
            self.stdout.write(
                self.style.WARNING('⚠️  No participants found')
            )
            return
        
        # Generate badges
        generated_count = 0
        updated_count = 0
        error_count = 0
        
        self.stdout.write('\n🔄 Starting badge generation...')
        
        for participant in participants:
            try:
                # Get or create badge
                badge, created = Badge.objects.get_or_create(
                    participant=participant,
                    defaults={'template': template}
                )
                
                # Set template if specified
                if template and badge.template != template:
                    badge.template = template
                    badge.save()
                
                # Generate or regenerate badge
                if not badge.is_generated or options['regenerate']:
                    self.stdout.write(f'   🎨 Generating badge for {participant.full_name}...')
                    badge.generate_badge()
                    
                    if created:
                        generated_count += 1
                        status = '✅ CREATED'
                    else:
                        updated_count += 1
                        status = '🔄 UPDATED'
                    
                    self.stdout.write(f'      {status} Badge generated successfully')
                else:
                    self.stdout.write(f'   ⏭️  Badge already exists for {participant.full_name}')
                
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'   ❌ Error generating badge for {participant.full_name}: {str(e)}')
                )
        
        # Summary
        self.stdout.write('\n' + '=' * 50)
        self.stdout.write(
            self.style.SUCCESS('🎉 Badge Generation Complete!')
        )
        self.stdout.write(f'📊 Statistics:')
        self.stdout.write(f'   ✅ Created: {generated_count} badges')
        self.stdout.write(f'   🔄 Updated: {updated_count} badges')
        self.stdout.write(f'   ❌ Errors: {error_count} badges')
        self.stdout.write(f'   📱 Total processed: {len(participants)} participants')
        
        if generated_count > 0 or updated_count > 0:
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n💡 Professional badges are ready!'
                    f'\n📁 Check the admin panel to view and download badges'
                    f'\n🌐 Access: /admin/badges/badge/'
                )
            )
        
        # Show template info
        if template:
            self.stdout.write(f'\n🎨 Template Used: {template.name}')
            self.stdout.write(f'   📐 Dimensions: {template.width}x{template.height}px')
            self.stdout.write(f'   🎨 Background: {template.background_color}')
            self.stdout.write(f'   📝 Description: {template.description}')
