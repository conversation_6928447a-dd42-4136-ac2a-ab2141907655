import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Alert, Form, Modal, Badge, Table, Image } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

interface Event {
  id: number;
  name: string;
  start_date: string;
  end_date: string;
}

interface GalleryImage {
  id: number;
  title: string;
  description: string;
  image: string;
  uploaded_at: string;
  is_featured: boolean;
  uploaded_by: string;
  event: number;
}

const EventGalleryManagement: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<string>('');
  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Modal states
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showBulkUploadModal, setShowBulkUploadModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingImage, setEditingImage] = useState<GalleryImage | null>(null);

  // Upload form states
  const [uploadForm, setUploadForm] = useState({
    title: '',
    description: '',
    image: null as File | null
  });

  const [bulkFiles, setBulkFiles] = useState<FileList | null>(null);
  const [selectedImages, setSelectedImages] = useState<number[]>([]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchEvents();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (selectedEvent) {
      fetchGalleryImages();
    }
  }, [selectedEvent]);

  const fetchEvents = async () => {
    try {
      const response = await api.get('/events/');
      setEvents(response.data.results || response.data);
    } catch (err) {
      setError('Failed to fetch events');
    }
  };

  const fetchGalleryImages = async () => {
    if (!selectedEvent) return;
    
    setLoading(true);
    try {
      const response = await api.get(`/gallery/?event=${selectedEvent}`);
      setGalleryImages(response.data.results || response.data);
    } catch (err) {
      setError('Failed to fetch gallery images');
    } finally {
      setLoading(false);
    }
  };

  const handleSingleUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!uploadForm.image || !selectedEvent) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('title', uploadForm.title);
      formData.append('description', uploadForm.description);
      formData.append('image', uploadForm.image);
      formData.append('event', selectedEvent);

      await api.post('/gallery/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      setSuccess('Image uploaded successfully!');
      setShowUploadModal(false);
      setUploadForm({ title: '', description: '', image: null });
      fetchGalleryImages();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to upload image');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!bulkFiles || !selectedEvent) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('event', selectedEvent);

      Array.from(bulkFiles).forEach((file, index) => {
        formData.append(`image_${index}`, file);
        formData.append(`title_${index}`, file.name.split('.')[0]);
        formData.append(`description_${index}`, '');
      });

      const response = await api.post('/gallery/bulk_upload/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      setSuccess(`Successfully uploaded ${response.data.uploaded} images!`);
      setShowBulkUploadModal(false);
      setBulkFiles(null);
      fetchGalleryImages();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to upload images');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleFeatured = async (imageId: number) => {
    try {
      await api.post(`/gallery/${imageId}/toggle_featured/`);
      setSuccess('Featured status updated!');
      fetchGalleryImages();
    } catch (err) {
      setError('Failed to update featured status');
    }
  };

  const handleDeleteImage = async (imageId: number) => {
    if (!window.confirm('Are you sure you want to delete this image?')) return;

    try {
      await api.delete(`/gallery/${imageId}/`);
      setSuccess('Image deleted successfully!');
      fetchGalleryImages();
    } catch (err) {
      setError('Failed to delete image');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedImages.length === 0) return;
    if (!window.confirm(`Are you sure you want to delete ${selectedImages.length} images?`)) return;

    try {
      await api.delete('/gallery/bulk_delete/', {
        data: { image_ids: selectedImages }
      });
      setSuccess(`Successfully deleted ${selectedImages.length} images!`);
      setSelectedImages([]);
      fetchGalleryImages();
    } catch (err) {
      setError('Failed to delete images');
    }
  };

  const handleImageSelect = (imageId: number) => {
    setSelectedImages(prev => 
      prev.includes(imageId) 
        ? prev.filter(id => id !== imageId)
        : [...prev, imageId]
    );
  };

  const handleSelectAll = () => {
    if (selectedImages.length === galleryImages.length) {
      setSelectedImages([]);
    } else {
      setSelectedImages(galleryImages.map(img => img.id));
    }
  };

  if (!isAuthenticated) {
    return (
      <Container className="py-5">
        <Alert variant="warning">Please log in to access gallery management.</Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      <Row>
        <Col>
          <h2 className="mb-4">
            <i className="fas fa-images me-2"></i>
            Event Gallery Management
          </h2>

          {error && (
            <Alert variant="danger" dismissible onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert variant="success" dismissible onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}

          {/* Event Selection */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Select Event</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <Form.Select
                    value={selectedEvent}
                    onChange={(e) => setSelectedEvent(e.target.value)}
                  >
                    <option value="">Choose an event...</option>
                    {events.map((event) => (
                      <option key={event.id} value={event.id}>
                        {event.name}
                      </option>
                    ))}
                  </Form.Select>
                </Col>
                <Col md={6}>
                  <Button
                    variant="primary"
                    onClick={() => setShowUploadModal(true)}
                    disabled={!selectedEvent}
                    className="me-2"
                  >
                    <i className="fas fa-plus me-1"></i>
                    Upload Single Image
                  </Button>
                  <Button
                    variant="info"
                    onClick={() => setShowBulkUploadModal(true)}
                    disabled={!selectedEvent}
                  >
                    <i className="fas fa-upload me-1"></i>
                    Bulk Upload
                  </Button>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* Gallery Images */}
          {selectedEvent && (
            <Card>
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Gallery Images ({galleryImages.length})</h5>
                <div>
                  {selectedImages.length > 0 && (
                    <>
                      <Badge bg="primary" className="me-2">
                        {selectedImages.length} selected
                      </Badge>
                      <Button
                        variant="danger"
                        size="sm"
                        onClick={handleBulkDelete}
                        className="me-2"
                      >
                        <i className="fas fa-trash me-1"></i>
                        Delete Selected
                      </Button>
                    </>
                  )}
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    onClick={handleSelectAll}
                  >
                    {selectedImages.length === galleryImages.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </div>
              </Card.Header>
              <Card.Body>
                {loading ? (
                  <div className="text-center py-4">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                ) : galleryImages.length === 0 ? (
                  <div className="text-center py-4">
                    <i className="fas fa-images fa-3x text-muted mb-3"></i>
                    <p className="text-muted">No images uploaded yet</p>
                  </div>
                ) : (
                  <Row>
                    {galleryImages.map((image) => (
                      <Col md={4} lg={3} key={image.id} className="mb-4">
                        <Card className={`h-100 ${selectedImages.includes(image.id) ? 'border-primary' : ''}`}>
                          <div className="position-relative">
                            <Image
                              src={image.image}
                              alt={image.title}
                              className="card-img-top"
                              style={{ height: '200px', objectFit: 'cover' }}
                            />
                            <Form.Check
                              type="checkbox"
                              className="position-absolute top-0 start-0 m-2"
                              checked={selectedImages.includes(image.id)}
                              onChange={() => handleImageSelect(image.id)}
                            />
                            {image.is_featured && (
                              <Badge
                                bg="warning"
                                className="position-absolute top-0 end-0 m-2"
                              >
                                Featured
                              </Badge>
                            )}
                          </div>
                          <Card.Body className="d-flex flex-column">
                            <Card.Title className="h6">{image.title}</Card.Title>
                            <Card.Text className="small text-muted flex-grow-1">
                              {image.description || 'No description'}
                            </Card.Text>
                            <div className="mt-auto">
                              <Button
                                variant="outline-warning"
                                size="sm"
                                onClick={() => handleToggleFeatured(image.id)}
                                className="me-1"
                              >
                                <i className={`fas fa-star ${image.is_featured ? '' : 'text-muted'}`}></i>
                              </Button>
                              <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => {
                                  setEditingImage(image);
                                  setShowEditModal(true);
                                }}
                                className="me-1"
                              >
                                <i className="fas fa-edit"></i>
                              </Button>
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => handleDeleteImage(image.id)}
                              >
                                <i className="fas fa-trash"></i>
                              </Button>
                            </div>
                          </Card.Body>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                )}
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>

      {/* Single Upload Modal */}
      <Modal show={showUploadModal} onHide={() => setShowUploadModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Upload Image</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSingleUpload}>
          <Modal.Body>
            <Form.Group className="mb-3">
              <Form.Label>Title</Form.Label>
              <Form.Control
                type="text"
                value={uploadForm.title}
                onChange={(e) => setUploadForm({...uploadForm, title: e.target.value})}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={uploadForm.description}
                onChange={(e) => setUploadForm({...uploadForm, description: e.target.value})}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Image</Form.Label>
              <Form.Control
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = (e.target as HTMLInputElement).files?.[0] || null;
                  setUploadForm({...uploadForm, image: file});
                }}
                required
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowUploadModal(false)}>
              Cancel
            </Button>
            <Button type="submit" variant="primary" disabled={loading}>
              {loading ? 'Uploading...' : 'Upload'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Bulk Upload Modal */}
      <Modal show={showBulkUploadModal} onHide={() => setShowBulkUploadModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Bulk Upload Images</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleBulkUpload}>
          <Modal.Body>
            <Form.Group className="mb-3">
              <Form.Label>Select Multiple Images</Form.Label>
              <Form.Control
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => setBulkFiles((e.target as HTMLInputElement).files)}
                required
              />
              <Form.Text className="text-muted">
                You can select multiple images. Titles will be auto-generated from filenames.
              </Form.Text>
            </Form.Group>
            {bulkFiles && (
              <div>
                <h6>Selected Files ({bulkFiles.length}):</h6>
                <ul className="list-unstyled">
                  {Array.from(bulkFiles).map((file, index) => (
                    <li key={index} className="small text-muted">
                      {file.name}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowBulkUploadModal(false)}>
              Cancel
            </Button>
            <Button type="submit" variant="primary" disabled={loading}>
              {loading ? 'Uploading...' : 'Upload All'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default EventGalleryManagement;
